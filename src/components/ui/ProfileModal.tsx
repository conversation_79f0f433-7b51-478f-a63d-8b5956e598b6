import React, { useState, useEffect, useRef } from 'react'
import * as Dialog from '@radix-ui/react-dialog'
import { X, User, Mail, Calendar, Shield, Globe, Star, Clock, Sparkles } from 'lucide-react'
import { cn } from '../../utils/cn'
import { formatLocalDateTime } from '../../utils/dateTimeHelper'
import { authService } from '../../services/auth/authService'
import { normalizePreferredTopics } from '../../utils/userUtils'
import * as anime from 'animejs'
import AnimatedCharacter from './AnimatedCharacter'
import SVGAnimated<PERSON>haracter from './SVGAnimatedCharacter'

interface PreferredTopic {
  name: string
  name_en: string
  id: string
}

interface ProfileData {
  username: string
  email: string
  role: string
  full_name: string
  auth_provider: string
  created_at: string
  last_login: string
  onboarding_completed: boolean
  age: number
  difficulty_level: number
  preferred_topics: string[] | PreferredTopic[]
  google_id?: string
  previous_login?: string
  profile_picture?: string
}

interface ProfileModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

/**
 * Profile Modal component showing detailed user information
 */
const ProfileModal: React.FC<ProfileModalProps> = ({
  open,
  onOpenChange
}) => {
  const [profileData, setProfileData] = useState<ProfileData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const avatarRef = useRef<HTMLDivElement>(null)
  const sparklesRef = useRef<HTMLDivElement>(null)
  const cardRef = useRef<HTMLDivElement>(null)

  // Fetch profile data when modal opens
  useEffect(() => {
    if (open && !profileData) {
      fetchProfileData()
    }
  }, [open, profileData])

  // Animate elements when modal opens
  useEffect(() => {
    if (open && profileData) {
      try {
        // Animate avatar
        if (avatarRef.current && anime.default) {
          anime.default({
            targets: avatarRef.current,
            scale: [0.8, 1.1, 1],
            rotate: [0, 360],
            duration: 1000,
            easing: 'easeOutElastic(1, .8)',
            delay: 200
          })
        }

        // Animate sparkles
        if (sparklesRef.current && anime.default) {
          anime.default({
            targets: sparklesRef.current.children,
            scale: [0, 1],
            opacity: [0, 1, 0],
            rotate: [0, 180],
            duration: 2000,
            delay: anime.default.stagger(100),
            loop: true,
            easing: 'easeInOutSine'
          })
        }

        // Animate profile card
        if (cardRef.current && anime.default) {
          anime.default({
            targets: cardRef.current,
            translateY: [50, 0],
            opacity: [0, 1],
            duration: 800,
            easing: 'easeOutCubic',
            delay: 400
          })
        }
      } catch (error) {
        console.warn('Anime.js animations failed to load:', error)
      }
    }
  }, [open, profileData])

  const fetchProfileData = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await authService.getUserProfile()
      setProfileData(data)
    } catch (err) {
      console.error('Failed to fetch profile data:', err)
      setError('Failed to load profile information')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    try {
      return formatLocalDateTime(dateString, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  const getDifficultyLabel = (level: number) => {
    switch (level) {
      case 1: return 'Beginner'
      case 2: return 'Intermediate'
      case 3: return 'Advanced'
      default: return `Level ${level}`
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin': return <Shield className="h-4 w-4 text-red-500" />
      case 'agent': return <Star className="h-4 w-4 text-blue-500" />
      default: return <User className="h-4 w-4 text-gray-500" />
    }
  }

  // Generate character type based on user data
  const getCharacterType = (userData: ProfileData) => {
    const characters = ['wizard', 'hero', 'student', 'scientist', 'artist', 'astronaut'] as const
    const index = userData.username.length % characters.length
    return characters[index]
  }

  // Generate mood based on user activity
  const getCharacterMood = (userData: ProfileData) => {
    const moods = ['happy', 'excited', 'thinking', 'celebrating', 'focused'] as const
    // Use age and difficulty level to determine mood
    const moodIndex = (userData.age + userData.difficulty_level) % moods.length
    return moods[moodIndex]
  }

  const generateSparkles = () => {
    return Array.from({ length: 8 }, (_, i) => (
      <div
        key={i}
        className="absolute w-2 h-2 bg-yellow-400 rounded-full"
        style={{
          left: `${20 + (i * 10)}%`,
          top: `${10 + (i % 3) * 20}%`,
        }}
      >
        ✨
      </div>
    ))
  }

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 z-50" />
        <Dialog.Content className="fixed left-[50%] top-[50%] z-50 grid w-[95vw] max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-4 sm:p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-lg max-h-[90vh] overflow-y-auto">
          
          {/* Header */}
          <div className="flex items-center justify-between">
            <Dialog.Title className="text-lg font-semibold">
              Profile Information
            </Dialog.Title>
            <Dialog.Close asChild>
              <button className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </button>
            </Dialog.Close>
          </div>

          {/* Content */}
          <div className="space-y-4">
            {loading && (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}

            {error && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
                <p className="text-destructive text-sm">{error}</p>
                <button 
                  onClick={fetchProfileData}
                  className="mt-2 text-sm text-primary hover:underline"
                >
                  Try again
                </button>
              </div>
            )}

            {profileData && (
              <div className="space-y-6">
                {/* Animated Profile Header */}
                <div className="relative">
                  {/* Sparkles Background */}
                  <div ref={sparklesRef} className="absolute inset-0 pointer-events-none">
                    {generateSparkles()}
                  </div>

                  {/* Profile Picture and Basic Info */}
                  <div ref={cardRef} className="relative bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl p-4 sm:p-6 border border-blue-200 dark:border-blue-800">
                    <div className="flex flex-col sm:flex-row items-center gap-4 sm:gap-6">
                      <div className="relative">
                        {profileData.profile_picture ? (
                          <div className="relative">
                            <img
                              ref={avatarRef}
                              src={profileData.profile_picture}
                              alt={profileData.full_name}
                              className="w-16 h-16 sm:w-20 sm:h-20 rounded-full object-cover shadow-lg border-4 border-white dark:border-gray-800"
                            />
                            {/* Floating badge */}
                            <div className="absolute -bottom-2 -right-2 bg-yellow-400 text-yellow-900 rounded-full p-1">
                              <Sparkles className="h-3 w-3" />
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center gap-2">
                            {/* SVG Animated Character */}
                            <SVGAnimatedCharacter
                              size={64}
                              mood={getCharacterMood(profileData)}
                              className="mb-2 sm:w-20 sm:h-20"
                            />
                            {/* Alternative Emoji Character */}
                            <AnimatedCharacter
                              size="sm"
                              character={getCharacterType(profileData)}
                              mood={getCharacterMood(profileData)}
                              className="opacity-60"
                            />
                          </div>
                        )}
                      </div>

                      <div className="flex-1 text-center sm:text-left">
                        <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                          {profileData.full_name}
                        </h3>
                        <p className="text-muted-foreground text-base sm:text-lg">@{profileData.username}</p>
                        <div className="flex items-center justify-center sm:justify-start gap-2 mt-2">
                          {getRoleIcon(profileData.role)}
                          <span className="text-sm capitalize bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded-full text-blue-700 dark:text-blue-300">
                            {profileData.role}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-3">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">Contact</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{profileData.email}</span>
                    </div>
                  </div>
                </div>

                {/* Learning Information */}
                <div className="space-y-3">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">Learning Profile</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Age: {profileData.age} years</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Difficulty: {getDifficultyLabel(profileData.difficulty_level)}</span>
                    </div>
                    {profileData.preferred_topics.length > 0 && (
                      <div className="flex items-start gap-3">
                        <User className="h-4 w-4 text-muted-foreground mt-0.5" />
                        <div>
                          <span className="text-sm">Preferred Topics:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {normalizePreferredTopics(profileData.preferred_topics).map((topic, index) => (
                              <div key={topic.id || index} className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full break-words">
                                <span className="font-medium">{topic.name}</span>
                                {topic.name_en && (
                                  <span className="text-primary/70 ml-1">({topic.name_en})</span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Account Information */}
                <div className="space-y-3">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">Account</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <span className="text-sm">Joined: </span>
                        <span className="text-sm text-muted-foreground">{formatDate(profileData.created_at)}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <span className="text-sm">Last Login: </span>
                        <span className="text-sm text-muted-foreground">{formatDate(profileData.last_login)}</span>
                      </div>
                    </div>
                    {profileData.previous_login && (
                      <div className="flex items-center gap-3">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <span className="text-sm">Previous Login: </span>
                          <span className="text-sm text-muted-foreground">{formatDate(profileData.previous_login)}</span>
                        </div>
                      </div>
                    )}
                    <div className="flex items-center gap-3">
                      <Shield className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Auth Provider: {profileData.auth_provider}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end">
            <Dialog.Close asChild>
              <button className="inline-flex h-10 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-semibold text-primary-foreground transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                Close
              </button>
            </Dialog.Close>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

export default ProfileModal
