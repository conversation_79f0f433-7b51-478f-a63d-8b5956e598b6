import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import * as Dialog from '@radix-ui/react-dialog'
import {
  X,
  ChevronLeft,
  ChevronRight,
  Loader2,
  AlertCircle,
  Edit3,
  FileText,
  Volume2,
  Image,
  Hash,
  CheckCircle,
  Clock,
  Save,
  RefreshCw
} from 'lucide-react'
import { ContentSet, CuratedService } from '../../services/curatedService'
import { cn } from '../../utils/cn'
import QuestionEditForm from './QuestionEditForm'

// Skeleton component for loading state
const QuestionEditSkeleton: React.FC = () => {
  return (
    <div className="bg-white dark:bg-slate-900 rounded-xl border border-slate-200 dark:border-slate-700 p-4 space-y-4 animate-pulse">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div>
          <div className="h-6 bg-slate-200 dark:bg-slate-700 rounded w-48 mb-2"></div>
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-32"></div>
        </div>
      </div>

      {/* Media section skeleton */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-4 border border-blue-200/50 dark:border-blue-800/30">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-5 h-5 bg-slate-200 dark:bg-slate-700 rounded"></div>
          <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-32"></div>
        </div>
        <div className="h-12 bg-slate-200 dark:bg-slate-700 rounded-lg"></div>
      </div>

      {/* Answer hint skeleton */}
      <div>
        <div className="flex items-center gap-3 mb-1">
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-20"></div>
          <div className="px-2 py-1 bg-blue-50 dark:bg-blue-950/30 rounded-md border border-blue-200/50 dark:border-blue-800/30">
            <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-24"></div>
          </div>
        </div>
        <div className="h-10 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
      </div>

      {/* Question text skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
        <div>
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-32 mb-1"></div>
          <div className="h-10 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
        </div>
        <div>
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-36 mb-1"></div>
          <div className="h-10 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
        </div>
      </div>

      {/* Answer configuration skeleton */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-lg p-3 border border-green-200/50 dark:border-green-800/30">
        <div className="flex items-center justify-between mb-2">
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-32"></div>
          <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-24"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="space-y-1">
            <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-16"></div>
            <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-20"></div>
          </div>
          <div className="space-y-1">
            <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-20"></div>
            <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-16"></div>
          </div>
        </div>
      </div>

      {/* Options skeleton */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-28"></div>
          <div className="h-6 bg-slate-200 dark:bg-slate-700 rounded w-20"></div>
        </div>
        <div className="space-y-3">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="p-3 rounded-md border-2 border-slate-200 dark:border-slate-700 space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-slate-200 dark:bg-slate-700 rounded"></div>
                  <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-16"></div>
                </div>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
                <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Action buttons skeleton */}
      <div className="flex items-center justify-end gap-2 pt-3 border-t border-slate-200 dark:border-slate-700">
        <div className="h-9 bg-slate-200 dark:bg-slate-700 rounded w-16"></div>
        <div className="h-9 bg-slate-200 dark:bg-slate-700 rounded w-28"></div>
      </div>
    </div>
  )
}

// Component to display set information on the left side
const SetInfoPanel: React.FC<{
  setDetails: any
  onSetUpdate?: (updatedSet: any) => void
}> = ({ setDetails, onSetUpdate }) => {
  const [editableData, setEditableData] = useState({
    title: '',
    title_en: '',
    description: '',
    description_en: '',
    thumbnail: '',
    status: 'pending',
    difficulty_level: 1
  })
  const [saving, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [saveSuccess, setSaveSuccess] = useState(false)

  if (!setDetails) return <div className="text-center py-8 text-muted-foreground">Loading set details...</div>

  // Update editable data when setDetails changes
  useEffect(() => {
    if (setDetails) {

      setEditableData({
        title: setDetails.title || '',
        title_en: setDetails.title_en || '',
        description: setDetails.description || '',
        description_en: setDetails.description_en || '',
        thumbnail: setDetails.thumbnail || '',
        status: setDetails.status || 'pending',
        difficulty_level: setDetails.difficulty_level || 1
      })
      setHasChanges(false)
      setSaveSuccess(false)
    }
  }, [setDetails])

  // Check for changes
  useEffect(() => {
    if (!setDetails) return

    const hasChanged =
      editableData.title !== (setDetails.title || '') ||
      editableData.title_en !== (setDetails.title_en || '') ||
      editableData.description !== (setDetails.description || '') ||
      editableData.description_en !== (setDetails.description_en || '') ||
      editableData.thumbnail !== (setDetails.thumbnail || '') ||
      editableData.status !== (setDetails.status || 'pending') ||
      editableData.difficulty_level !== (setDetails.difficulty_level || 1)



    setHasChanges(hasChanged)
  }, [editableData, setDetails])

  const handleInputChange = (field: string, value: string | number) => {
    setEditableData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    if (!setDetails) return

    try {
      setSaving(true)

      // Prepare update data (only send changed fields)
      const updateData: any = {}

      if (editableData.title !== (setDetails.title || '')) {
        updateData.title = editableData.title
      }
      if (editableData.title_en !== (setDetails.title_en || '')) {
        updateData.title_en = editableData.title_en
      }
      if (editableData.description !== (setDetails.description || '')) {
        updateData.description = editableData.description
      }
      if (editableData.description_en !== (setDetails.description_en || '')) {
        updateData.description_en = editableData.description_en
      }
      if (editableData.thumbnail !== (setDetails.thumbnail || '')) {
        updateData.thumbnail = editableData.thumbnail
      }
      if (editableData.status !== (setDetails.status || 'pending')) {
        updateData.status = editableData.status
      }
      if (editableData.difficulty_level !== (setDetails.difficulty_level || 1)) {
        updateData.difficulty_level = editableData.difficulty_level
      }

      if (Object.keys(updateData).length === 0) {
        return
      }

      const response = await CuratedService.updateCuratedSet(setDetails.id, updateData)

      if (response.success) {
        setSaveSuccess(true)
        setTimeout(() => setSaveSuccess(false), 3000)

        // Notify parent component of the update
        if (onSetUpdate) {
          onSetUpdate(response.data)
        }
      }
    } catch (error) {
      console.error('Error saving set details:', error)
    } finally {
      setSaving(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getInputTypeIcon = (inputType: string) => {
    switch (inputType) {
      case 'audio':
        return <Volume2 className="w-4 h-4" />
      case 'image':
        return <Image className="w-4 h-4" />
      default:
        return <FileText className="w-4 h-4" />
    }
  }



  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-4">
      {/* Set Information Success Message */}
      {saveSuccess && (
        <div className="bg-green-50 dark:bg-green-950/30 rounded-xl p-3 border border-green-200/50 dark:border-green-800/30">
          <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm font-medium">Set information saved successfully!</span>
          </div>
        </div>
      )}

      {/* Title and Basic Info */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-3 border border-blue-200/50 dark:border-blue-800/30">
        <div className="space-y-2">
          <div>
            <label className="text-xs font-medium text-muted-foreground mb-1 block">Title (Nepali)</label>
            <input
              value={editableData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="w-full p-2 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="text-xs font-medium text-muted-foreground mb-1 block">Title (English)</label>
            <input
              value={editableData.title_en}
              onChange={(e) => handleInputChange('title_en', e.target.value)}
              className="w-full p-2 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {getInputTypeIcon(setDetails.input_type)}
            <span className="capitalize">{setDetails.input_type}</span>
          </div>
        </div>
      </div>

      {/* Descriptions */}
      <div className="bg-white dark:bg-slate-800 rounded-xl p-3 border border-slate-200 dark:border-slate-700">
        <h4 className="text-sm font-medium text-foreground mb-2">Descriptions</h4>
        <div className="space-y-2">
          <div>
            <label className="text-xs font-medium text-muted-foreground mb-1 block">Description (Nepali)</label>
            <textarea
              value={editableData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="w-full p-2 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={2}
            />
          </div>
          <div>
            <label className="text-xs font-medium text-muted-foreground mb-1 block">Description (English)</label>
            <textarea
              value={editableData.description_en}
              onChange={(e) => handleInputChange('description_en', e.target.value)}
              className="w-full p-2 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={2}
            />
          </div>
        </div>
      </div>

      {/* Thumbnail */}
      <div className="bg-white dark:bg-slate-800 rounded-xl p-3 border border-slate-200 dark:border-slate-700">
        <h4 className="text-sm font-medium text-foreground mb-2 flex items-center gap-2">
          <Image className="w-4 h-4" />
          Thumbnail
        </h4>
        <div className="space-y-2">
          <div>
            <label className="text-xs font-medium text-muted-foreground mb-1 block">Thumbnail Name</label>
            <input
              value={editableData.thumbnail}
              onChange={(e) => handleInputChange('thumbnail', e.target.value)}
              className="w-full p-2 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter thumbnail keyword (e.g., River, Mountain, etc.)"
            />
          </div>
          {setDetails.thumbnail_metadata?.url && (
            <div>
              <img
                src={setDetails.thumbnail_metadata.url}
                alt={setDetails.title}
                className="w-full h-32 object-cover rounded-lg"
              />
              <p className="text-xs text-muted-foreground mt-1">
                {setDetails.thumbnail_metadata.file_name} • {formatBytes(setDetails.thumbnail_metadata.size_bytes)}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Statistics */}
      <div className="bg-white dark:bg-slate-800 rounded-xl p-3 border border-slate-200 dark:border-slate-700">
        <h4 className="text-sm font-medium text-foreground mb-2 flex items-center gap-2">
          <Hash className="w-4 h-4" />
          Statistics
        </h4>
        <div className="grid grid-cols-2 gap-2">
          <div className="text-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
            <div className="text-lg font-semibold text-foreground">{setDetails.total_tasks || 0}</div>
            <div className="text-xs text-muted-foreground">Tasks</div>
          </div>
          <div className="text-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
            <div className="text-lg font-semibold text-foreground">{setDetails.total_stories || 0}</div>
            <div className="text-xs text-muted-foreground">Stories</div>
          </div>
          <div className="text-center p-2 bg-green-50 dark:bg-green-950/30 rounded-lg">
            <div className="text-lg font-semibold text-green-600">{setDetails.text_tasks_ready || 0}</div>
            <div className="text-xs text-green-600">Ready</div>
          </div>
          <div className="text-center p-2 bg-yellow-50 dark:bg-yellow-950/30 rounded-lg">
            <div className="text-lg font-semibold text-yellow-600">{setDetails.media_tasks_pending || 0}</div>
            <div className="text-xs text-yellow-600">Pending</div>
          </div>
        </div>
      </div>

      {/* Input Content */}
      {setDetails.input_content && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-xl p-3 border border-green-200/50 dark:border-green-800/30">
          <h4 className="text-sm font-medium text-foreground mb-2 flex items-center gap-2">
            <Volume2 className="w-4 h-4" />
            Input Content
          </h4>
          <div className="space-y-2">
            <div>
              <label className="text-xs font-medium text-muted-foreground mb-1 block">Original Prompt</label>
              <textarea
                value={setDetails.input_content.original_prompt || ''}
                className="w-full p-2 text-xs bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-md resize-none"
                rows={3}
              />
            </div>
            <div>
              <label className="text-xs font-medium text-muted-foreground mb-1 block">Script</label>
              <textarea
                value={setDetails.input_content.script || ''}
                className="w-full p-2 text-xs bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-md resize-none"
                rows={4}
              />
            </div>
            {setDetails.input_content.url && (
              <div>
                <label className="text-xs font-medium text-muted-foreground mb-1 block">Audio Player</label>
                <audio
                  controls
                  className="w-full"
                  preload="metadata"
                >
                  <source src={setDetails.input_content.url} type={setDetails.input_content.content_type} />
                  Your browser does not support the audio element.
                </audio>
                <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                  <span>Length: {setDetails.input_content.script_length} chars</span>
                  <span>Size: {formatBytes(setDetails.input_content.audio_length_bytes)}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Status and Dates */}
      <div className="bg-white dark:bg-slate-800 rounded-xl p-3 border border-slate-200 dark:border-slate-700">
        <h4 className="text-sm font-medium text-foreground mb-2 flex items-center gap-2">
          <Clock className="w-4 h-4" />
          Status & Timeline
        </h4>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Status</span>
            <select
              value={editableData.status}
              onChange={(e) => handleInputChange('status', e.target.value)}
              className="px-2 py-1 text-xs font-medium border border-slate-200 dark:border-slate-600 rounded-md bg-white dark:bg-slate-800 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="in_progress">In Progress</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Created</span>
            <span className="text-xs text-foreground">
              {formatDate(setDetails.created_at)}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Updated</span>
            <span className="text-xs text-foreground">
              {formatDate(setDetails.updated_at)}
            </span>
          </div>
        </div>
      </div>

      {/* Generation Type */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 rounded-xl p-3 border border-purple-200/50 dark:border-purple-800/30">
        <h4 className="text-sm font-medium text-foreground mb-2 flex items-center gap-2">
          <CheckCircle className="w-4 h-4" />
          Generation Info
        </h4>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Type</span>
            <span className="text-sm text-foreground capitalize">{setDetails.gentype}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Follow-up</span>
            <span className="text-sm text-foreground">
              {setDetails.has_follow_up ? 'Yes' : 'No'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Difficulty</span>
            <select
              value={editableData.difficulty_level}
              onChange={(e) => handleInputChange('difficulty_level', parseInt(e.target.value))}
              className="px-2 py-1 text-xs font-medium border border-slate-200 dark:border-slate-600 rounded-md bg-white dark:bg-slate-800 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={1}>Easy (1)</option>
              <option value={2}>Medium (2)</option>
              <option value={3}>Hard (3)</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Theme ID</span>
            <span className="text-xs text-foreground font-mono">{setDetails.theme_id}</span>
          </div>
        </div>
      </div>

      {/* Set Information Save Button - Bottom */}
      {hasChanges && (
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-950/30 dark:to-amber-950/30 rounded-xl p-4 border border-orange-200/50 dark:border-orange-800/30 sticky bottom-0">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm font-medium text-orange-700 dark:text-orange-300 block">
                Set Information Changes
              </span>
              <span className="text-xs text-orange-600 dark:text-orange-400">
                Title, description, status, etc.
              </span>
            </div>
            <button
              onClick={handleSave}
              disabled={saving}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:from-orange-300 disabled:to-orange-400 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-sm"
            >
              {saving ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Saving Set...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Save Set Info
                </>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

interface SetEditModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  contentSet: ContentSet | null
}

interface TaskData {
  id: string
  type: string
  title: string
  question: {
    text: string
    translated_text: string
    options: Record<string, string>
    options_en: Record<string, string>
    correct_answer_index: number
    answer_hint?: string
  }
  correct_answer: {
    text: string
    index: number
    explanation?: string
  }
  status: string
  difficulty_level: number
}

/**
 * Modal for editing questions in a curated content set
 * Fetches set details and individual task data for editing
 */
const SetEditModal: React.FC<SetEditModalProps> = ({
  open,
  onOpenChange,
  contentSet
}) => {
  const [setDetails, setSetDetails] = useState<any>(null)
  const [tasks, setTasks] = useState<TaskData[]>([])
  const [currentTaskIndex, setCurrentTaskIndex] = useState(0)
  const [loading, setLoading] = useState(false)
  const [loadingTasks, setLoadingTasks] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [saving, setSaving] = useState(false)

  // Fetch set details and tasks when modal opens
  useEffect(() => {
    if (open && contentSet) {
      fetchSetDetails()
    }
  }, [open, contentSet])

  const fetchSetDetails = async () => {
    if (!contentSet) return

    try {
      setLoading(true)
      setError(null)

      // Fetch set details
      const setResponse = await CuratedService.getCuratedSetById(contentSet.id)
      setSetDetails(setResponse.data)

      // Only fetch the first task initially (not all tasks)
      if (setResponse.data.tasks && setResponse.data.tasks.length > 0) {
        await fetchSingleTask(setResponse.data.tasks[0])
      }
    } catch (err) {
      console.error('Error fetching set details:', err)
      setError('Failed to load set details. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const fetchSingleTask = async (taskId: string) => {
    try {
      setLoadingTasks(true)
      const taskResponse = await CuratedService.getCuratedTaskById(taskId)
      const taskData = taskResponse.data

      // Update the tasks array with the fetched task
      setTasks(prev => {
        const newTasks = [...prev]
        const existingIndex = newTasks.findIndex(t => t.id === taskData.id)
        if (existingIndex >= 0) {
          newTasks[existingIndex] = taskData
        } else {
          newTasks.push(taskData)
        }
        return newTasks
      })
    } catch (err) {
      console.error('Error fetching task:', err)
      setError('Failed to load task details. Please try again.')
    } finally {
      setLoadingTasks(false)
    }
  }

  const handleSaveQuestion = async (taskId: string, updatedData: any) => {
    try {
      setSaving(true)
      console.log('Saving question:', taskId, updatedData)

      const response = await CuratedService.updateCuratedTaskQuestion(taskId, updatedData)
      console.log('Save response:', response)

      // Force refresh the current task data (no caching)
      await refreshCurrentTask()
    } catch (err) {
      console.error('Error saving question:', err)
      throw err
    } finally {
      setSaving(false)
    }
  }

  const refreshCurrentTask = async () => {
    if (!setDetails?.tasks || currentTaskIndex < 0) return

    const taskId = setDetails.tasks[currentTaskIndex]
    try {
      setLoadingTasks(true)
      console.log('Refreshing task:', taskId)

      const updatedTaskResponse = await CuratedService.getCuratedTaskById(taskId)
      const updatedTask = updatedTaskResponse.data

      console.log('Refreshed task data:', updatedTask)

      setTasks(prev => {
        const newTasks = prev.filter(t => t.id !== taskId) // Remove old version
        newTasks.push(updatedTask) // Add fresh version
        return newTasks
      })
    } catch (err) {
      console.error('Error refreshing task:', err)
    } finally {
      setLoadingTasks(false)
    }
  }

  const handleClose = () => {
    setSetDetails(null)
    setTasks([])
    setCurrentTaskIndex(0)
    setError(null)
    onOpenChange(false)
  }

  // Navigate to a specific task index and always fetch fresh data
  const navigateToTask = async (index: number) => {
    if (!setDetails?.tasks) return

    setCurrentTaskIndex(index)
    const taskId = setDetails.tasks[index]

    // Always fetch fresh data to avoid cache issues
    console.log('Navigating to task:', index, taskId)
    await fetchSingleTask(taskId)
  }

  const currentTask = tasks.find(t => t.id === setDetails?.tasks?.[currentTaskIndex])
  const hasMultipleTasks = setDetails?.tasks?.length > 1

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-[98vw] max-w-[1400px] max-h-[95vh] overflow-hidden">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-slate-900 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Edit3 className="w-5 h-5 text-blue-500" />
                  <span className="text-sm text-muted-foreground font-medium">
                    Edit Questions
                  </span>
                </div>
                <div>
                  <Dialog.Title className="text-xl font-semibold text-slate-900 dark:text-white">
                    {setDetails?.title || contentSet?.title}
                  </Dialog.Title>
                  {hasMultipleTasks && (
                    <Dialog.Description className="text-sm text-slate-600 dark:text-slate-400">
                      Question {currentTaskIndex + 1} of {setDetails?.tasks?.length || 0}
                    </Dialog.Description>
                  )}
                </div>
              </div>
              <Dialog.Close onClick={handleClose} className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                <X className="w-5 h-5" />
              </Dialog.Close>
            </div>

            {/* Navigation for multiple tasks */}
            {hasMultipleTasks && (
              <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800/50 border-b border-slate-200 dark:border-slate-700">
                <button
                  onClick={() => navigateToTask(Math.max(0, currentTaskIndex - 1))}
                  disabled={currentTaskIndex === 0}
                  className={cn(
                    'flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    currentTaskIndex === 0
                      ? 'text-muted-foreground cursor-not-allowed'
                      : 'text-foreground hover:bg-slate-200 dark:hover:bg-slate-700'
                  )}
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </button>

                <div className="flex items-center gap-2">
                  {setDetails?.tasks?.map((taskId: string, index: number) => (
                    <button
                      key={taskId}
                      onClick={() => navigateToTask(index)}
                      className={cn(
                        'w-8 h-8 rounded-full text-xs font-medium transition-colors',
                        index === currentTaskIndex
                          ? 'bg-blue-500 text-white'
                          : 'bg-slate-200 dark:bg-slate-700 text-muted-foreground hover:bg-slate-300 dark:hover:bg-slate-600'
                      )}
                    >
                      {index + 1}
                    </button>
                  ))}
                </div>

                <button
                  onClick={() => navigateToTask(Math.min((setDetails?.tasks?.length || 1) - 1, currentTaskIndex + 1))}
                  disabled={currentTaskIndex === (setDetails?.tasks?.length || 1) - 1}
                  className={cn(
                    'flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    currentTaskIndex === (setDetails?.tasks?.length || 1) - 1
                      ? 'text-muted-foreground cursor-not-allowed'
                      : 'text-foreground hover:bg-slate-200 dark:hover:bg-slate-700'
                  )}
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            )}

            {/* Content - Two Column Layout */}
            <div className="flex h-[calc(95vh-200px)]">
              {loading ? (
                <div className="flex items-center justify-center py-12 w-full">
                  <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                  <span className="ml-3 text-slate-600 dark:text-slate-400">
                    Loading set details...
                  </span>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center py-12 text-red-500 w-full">
                  <AlertCircle className="w-8 h-8" />
                  <span className="ml-3">{error}</span>
                </div>
              ) : setDetails ? (
                <>
                  {/* Left Column - Set Information (2/5 width) */}
                  <div className="w-2/5 p-4 overflow-y-auto border-r border-slate-200 dark:border-slate-700">
                    <SetInfoPanel
                      setDetails={setDetails}
                      onSetUpdate={(updatedSet) => {
                        setSetDetails(updatedSet)
                      }}
                    />
                  </div>

                  {/* Right Column - Question Edit Form (3/5 width) */}
                  <div className="w-3/5 p-4 overflow-y-auto">
                    {loadingTasks ? (
                      <QuestionEditSkeleton />
                    ) : currentTask ? (
                      <AnimatePresence mode="wait">
                        <motion.div
                          key={currentTaskIndex}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          transition={{ duration: 0.2 }}
                        >
                          <QuestionEditForm
                            questionData={currentTask}
                            onSave={handleSaveQuestion}
                            onCancel={handleClose}
                            onSaveSuccess={refreshCurrentTask}
                            saving={saving}
                          />
                        </motion.div>
                      </AnimatePresence>
                    ) : (
                      <div className="text-center py-12 text-slate-500">
                        No questions available for this content set.
                      </div>
                    )}
                  </div>
                </>
              ) : (
                <div className="text-center py-12 text-slate-500 w-full">
                  No set details available.
                </div>
              )}
            </div>
          </motion.div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

export default SetEditModal
