import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import * as Dialog from '@radix-ui/react-dialog'
import {
  X,
  Play,
  BookOpen,
  Clock,
  Loader2,
  AlertCircle,
  ExternalLink,
  CheckCircle
} from 'lucide-react'
import { ContentSet, Question, CuratedService } from '../../services/curatedService'
import { cn } from '../../utils/cn'
import { QuestionsList } from './QuestionsList'

interface QuestionsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  contentSet: ContentSet | null
  onTryThis: (taskSetId: string) => void
}

/**
 * Modal for displaying questions from a curated content set
 * with a "Try This" button to convert to task set
 */
const QuestionsModal: React.FC<QuestionsModalProps> = ({
  open,
  onOpenChange,
  contentSet,
  onTryThis
}) => {
  const [questions, setQuestions] = useState<Question[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [converting, setConverting] = useState(false)
  const [showExistsPopup, setShowExistsPopup] = useState(false)
  const [existingTaskSetId, setExistingTaskSetId] = useState<string | null>(null)

  // Fetch questions when modal opens and content set is available
  useEffect(() => {
    if (open && contentSet) {
      fetchQuestions()
    }
  }, [open, contentSet])

  const fetchQuestions = async () => {
    if (!contentSet) return

    setLoading(true)
    setError(null)
    try {
      const questionsData = await CuratedService.getQuestions(contentSet.id || contentSet._id)
      setQuestions(questionsData)
    } catch (err) {
      console.error('Error fetching questions:', err)
      setError('Failed to load questions. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleTryThis = async () => {
    if (!contentSet) return

    setConverting(true)
    setError(null)
    try {
      const response = await CuratedService.convertToTaskSet(contentSet.id || contentSet._id)
      console.log('Convert response status:', response.status) // Debug log
      console.log('Convert response:', response) // Debug log

      // Handle both possible response structures
      // Sometimes response.data exists, sometimes data is directly in response
      const responseData = response.data || (response as any)
      console.log('Response data:', responseData) // Debug log

      // Check if task set already exists - check message first since 201 is still success
      if (responseData.message === "Task set already exists for user") {
        setExistingTaskSetId(responseData.task_set_id)
        setShowExistsPopup(true)
        return
      }

      // Handle successful creation (new task set)
      const taskSetId = responseData.task_set_id
      if (taskSetId) {
        onTryThis(taskSetId)
        onOpenChange(false)
      } else {
        setError('No task set ID returned from server.')
      }
    } catch (err) {
      console.error('Error converting to task set:', err)
      setError('Failed to create task set. Please try again.')
    } finally {
      setConverting(false)
    }
  }

  const handleNavigateToExisting = () => {
    if (existingTaskSetId) {
      onTryThis(existingTaskSetId)
      setShowExistsPopup(false)
      onOpenChange(false)
    }
  }

  const handleCloseExistsPopup = () => {
    setShowExistsPopup(false)
    setExistingTaskSetId(null)
  }

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'text-green-600 bg-green-50 border-green-200'
      case 2: return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 3: return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getDifficultyLabel = (level: number) => {
    switch (level) {
      case 1: return 'Easy'
      case 2: return 'Medium'
      case 3: return 'Hard'
      default: return 'Unknown'
    }
  }

  if (!contentSet) return null

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-[95vw] max-w-4xl max-h-[90vh] overflow-hidden">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-slate-900 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-4">
                {contentSet.theme && (
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{contentSet.theme.icon}</span>
                    <span className="text-sm text-muted-foreground font-medium">
                      {contentSet.theme.name_en}
                    </span>
                  </div>
                )}
                <div>
                  <Dialog.Title className="text-xl font-semibold text-slate-900 dark:text-white">
                    {contentSet.title_en || contentSet.title}
                  </Dialog.Title>
                  <Dialog.Description className="text-sm text-slate-600 dark:text-slate-400">
                    {contentSet.description_en || contentSet.description}
                  </Dialog.Description>
                </div>
              </div>
              <Dialog.Close className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                <X className="w-5 h-5" />
              </Dialog.Close>
            </div>

            {/* Content Info */}
            <div className="px-4 sm:px-6 py-4 bg-slate-50 dark:bg-slate-800/50 border-b border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <BookOpen className="w-4 h-4 text-blue-500" />
                  <span className="text-slate-600 dark:text-slate-400">
                    {contentSet.total_items} Questions
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-green-500" />
                  <span className="text-slate-600 dark:text-slate-400">
                    ~{Math.ceil(contentSet.total_items * 1.5)} min
                  </span>
                </div>
                <div className={cn(
                  'px-2 py-1 rounded-full text-xs font-medium border',
                  getDifficultyColor(contentSet.difficulty_level)
                )}>
                  {getDifficultyLabel(contentSet.difficulty_level)}
                </div>
              </div>
            </div>

            {/* Questions Content */}
            <div className="p-4 sm:p-6 max-h-96 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                  <span className="ml-3 text-slate-600 dark:text-slate-400">
                    Loading questions...
                  </span>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center py-12 text-red-500">
                  <AlertCircle className="w-8 h-8" />
                  <span className="ml-3">{error}</span>
                </div>
              ) : questions.length === 0 ? (
                <div className="text-center py-12 text-slate-500">
                  No questions available for this content set.
                </div>
              ) : (
                <QuestionsList questions={questions} />
              )}
            </div>

            {/* Footer with Try This button */}
            <div className="px-4 sm:px-6 py-4 bg-slate-50 dark:bg-slate-800/50 border-t border-slate-200 dark:border-slate-700">
              <div className="flex items-center justify-between">
                <div className="text-sm text-slate-600 dark:text-slate-400">
                  Ready to start this quiz?
                </div>
                <button
                  onClick={handleTryThis}
                  disabled={converting || loading}
                  className={cn(
                    'px-6 py-2 rounded-lg font-medium transition-all duration-200',
                    'bg-gradient-to-r from-blue-500 to-indigo-600 text-white',
                    'hover:from-blue-600 hover:to-indigo-700 hover:shadow-lg',
                    'disabled:opacity-50 disabled:cursor-not-allowed',
                    'flex items-center gap-2'
                  )}
                >
                  {converting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      Try This
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </Dialog.Content>
      </Dialog.Portal>

      {/* Task Set Already Exists Popup */}
      {showExistsPopup && (
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[60]" />
          <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[60] w-[90vw] max-w-md">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-slate-900 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 overflow-hidden"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                      Quiz Already Added
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      You've already added this quiz to your collection
                    </p>
                  </div>
                </div>
                <button
                  onClick={handleCloseExistsPopup}
                  className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6">
                <p className="text-slate-700 dark:text-slate-300 mb-6">
                  This quiz has already been added to your collection. Would you like to navigate to your existing quiz?
                </p>

                {/* Actions */}
                <div className="flex gap-3">
                  <button
                    onClick={handleCloseExistsPopup}
                    className="flex-1 px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleNavigateToExisting}
                    className={cn(
                      'flex-1 px-4 py-2 rounded-lg font-medium transition-all duration-200',
                      'bg-gradient-to-r from-blue-500 to-indigo-600 text-white',
                      'hover:from-blue-600 hover:to-indigo-700 hover:shadow-lg',
                      'flex items-center justify-center gap-2'
                    )}
                  >
                    <ExternalLink className="w-4 h-4" />
                    Go to Quiz
                  </button>
                </div>
              </div>
            </motion.div>
          </Dialog.Content>
        </Dialog.Portal>
      )}
    </Dialog.Root>
  )
}

export default QuestionsModal
