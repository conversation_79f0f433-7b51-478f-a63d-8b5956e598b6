import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Arrow<PERSON>ef<PERSON>,
  ChevronRight,
  Target,
  BarChart3,
  TrendingUp,
  Info,
  Calendar
} from 'lucide-react'
import { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsDailyInteractiveDrillDownProps {
  data: TaskSetsMetrics
}

interface DrillDownLevel {
  id: string
  name: string
  level: number
  path: string[]
  selectedDate?: string
  selectedGentype?: string
}

interface DailyColumnData {
  id: string
  name: string
  value: number
  taskItems?: number
  avgSet?: number
  color: string
  date?: string
  percentage?: number
  breakdown?: Array<{
    gentype: string
    task_sets_count: number
    task_items_count: number
    task_types: Array<{
      task_type: string
      count: number
    }>
  }>
  taskTypes?: Array<{
    task_type: string
    count: number
  }>
}

const TaskSetsDailyInteractiveDrillDown: React.FC<TaskSetsDailyInteractiveDrillDownProps> = ({ data }) => {
  const [currentLevel, setCurrentLevel] = useState<DrillDownLevel>({
    id: 'root',
    name: 'Daily Task Sets',
    level: 0,
    path: []
  })
  const [hoveredColumn, setHoveredColumn] = useState<string | null>(null)

  // Color palette for daily data
  const dailyColors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
  ]

  // Transform daily data for visualization
  const transformedDailyData = useMemo(() => {
    if (!data?.daily_data || data.daily_data.length === 0) return []

    return data.daily_data.map((day, index) => ({
      id: day.date,
      name: new Date(day.date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      }),
      value: day.total_task_sets,
      taskItems: day.total_task_items,
      avgSet: day.total_task_items > 0 ? day.total_task_items / day.total_task_sets : 0,
      color: dailyColors[index % dailyColors.length],
      date: day.date,
      breakdown: day.gentype_breakdown || null
    }))
  }, [data])

  // Transform breakdown data for a specific day
  const transformDayBreakdown = useMemo(() => {
    if (currentLevel.level === 0 || !currentLevel.selectedDate) return []

    const selectedDay = data?.daily_data?.find(day => day.date === currentLevel.selectedDate)
    if (!selectedDay?.gentype_breakdown) return []

    return selectedDay.gentype_breakdown.map((gentype, index) => ({
      id: gentype.gentype,
      name: gentype.gentype,
      value: gentype.task_sets_count,
      taskItems: gentype.task_items_count,
      color: dailyColors[index % dailyColors.length],
      percentage: 0, // Will be calculated below
      taskTypes: gentype.task_types
    }))
  }, [data, currentLevel, dailyColors])

  // Transform task types data for a specific gentype
  const transformTaskTypesBreakdown = useMemo(() => {
    if (currentLevel.level !== 2 || !currentLevel.selectedDate || !currentLevel.selectedGentype) return []

    const selectedDay = data?.daily_data?.find(day => day.date === currentLevel.selectedDate)
    if (!selectedDay?.gentype_breakdown) return []

    const selectedGentype = selectedDay.gentype_breakdown.find(g => g.gentype === currentLevel.selectedGentype)
    if (!selectedGentype?.task_types) return []

    return selectedGentype.task_types.map((taskType, index) => ({
      id: taskType.task_type,
      name: taskType.task_type,
      value: taskType.count,
      color: dailyColors[index % dailyColors.length],
      percentage: 0 // Will be calculated below
    }))
  }, [data, currentLevel, dailyColors])

  // Get current level data
  const currentData = useMemo(() => {
    if (currentLevel.level === 0) {
      return transformedDailyData
    } else if (currentLevel.level === 1) {
      // Calculate percentages for gentype breakdown
      const total = transformDayBreakdown.reduce((sum, item) => sum + item.value, 0)
      return transformDayBreakdown.map(item => ({
        ...item,
        percentage: total > 0 ? (item.value / total) * 100 : 0
      }))
    } else if (currentLevel.level === 2) {
      // Calculate percentages for task types breakdown
      const total = transformTaskTypesBreakdown.reduce((sum, item) => sum + item.value, 0)
      return transformTaskTypesBreakdown.map(item => ({
        ...item,
        percentage: total > 0 ? (item.value / total) * 100 : 0
      }))
    }
    return []
  }, [transformedDailyData, transformDayBreakdown, transformTaskTypesBreakdown, currentLevel])

  // Calculate percentages for current level
  const dataWithPercentages = useMemo(() => {
    if (currentLevel.level > 0) return currentData // Already calculated

    const total = currentData.reduce((sum, item: DailyColumnData) => sum + item.value, 0)
    return currentData.map((item: DailyColumnData) => ({
      ...item,
      percentage: total > 0 ? (item.value / total) * 100 : 0
    }))
  }, [currentData, currentLevel])

  // Get max value for scaling bars
  const maxValue = useMemo(() => {
    return Math.max(...currentData.map((item: DailyColumnData) => item.value), 1)
  }, [currentData])

  const handleColumnClick = (column: DailyColumnData) => {
    if (currentLevel.level === 0 && column.breakdown && column.breakdown.length > 0) {
      // Drill down into daily breakdown (level 0 → level 1)
      setCurrentLevel({
        id: column.id,
        name: `${column.name} Breakdown`,
        level: 1,
        path: [column.id],
        selectedDate: column.date
      })
    } else if (currentLevel.level === 1 && column.taskTypes && column.taskTypes.length > 0) {
      // Drill down into task types breakdown (level 1 → level 2)
      setCurrentLevel({
        id: column.id,
        name: `${formatColumnName(column.name)} Task Types`,
        level: 2,
        path: [...currentLevel.path, column.id],
        selectedDate: currentLevel.selectedDate,
        selectedGentype: column.id
      })
    }
  }

  const handleBackClick = () => {
    if (currentLevel.level === 2) {
      // Go back to gentype level (level 2 → level 1)
      setCurrentLevel({
        id: currentLevel.path[0],
        name: `${new Date(currentLevel.selectedDate!).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        })} Breakdown`,
        level: 1,
        path: [currentLevel.path[0]],
        selectedDate: currentLevel.selectedDate
      })
    } else if (currentLevel.level === 1) {
      // Go back to daily level (level 1 → level 0)
      setCurrentLevel({
        id: 'root',
        name: 'Daily Task Sets',
        level: 0,
        path: []
      })
    }
  }

  const handleBreadcrumbClick = (targetLevel: number) => {
    if (targetLevel === 0) {
      // Navigate to daily level
      setCurrentLevel({
        id: 'root',
        name: 'Daily Task Sets',
        level: 0,
        path: []
      })
    } else if (targetLevel === 1 && currentLevel.selectedDate) {
      // Navigate to gentype level
      setCurrentLevel({
        id: currentLevel.path[0],
        name: `${new Date(currentLevel.selectedDate).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        })} Breakdown`,
        level: 1,
        path: [currentLevel.path[0]],
        selectedDate: currentLevel.selectedDate
      })
    }
  }

  const formatColumnName = (name: string) => {
    return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  if (!data || dataWithPercentages.length === 0) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center space-y-2">
          <Calendar className="h-12 w-12 text-muted-foreground mx-auto" />
          <p className="text-muted-foreground">No daily data available</p>
        </div>
      </div>
    )
  }

  // Get latest date from the data
  const latestDate = useMemo(() => {
    if (!data?.daily_data || data.daily_data.length === 0) return null
    const dates = data.daily_data.map(day => new Date(day.date))
    const latest = new Date(Math.max(...dates.map(date => date.getTime())))
    return latest.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }, [data])

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Navigation Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {currentLevel.level > 0 && (
            <button
              onClick={handleBackClick}
              className="flex items-center gap-2 px-3 py-2 rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors text-sm border border-primary/20"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </button>
          )}

          {/* Latest Date Display */}
          {latestDate && (
            <div className="flex items-center gap-2 px-3 py-2 bg-muted/50 rounded-lg">
              <Calendar className="w-4 h-4 text-primary" />
              <div className="text-sm">
                <span className="text-muted-foreground">Latest: </span>
                <span className="font-medium text-foreground">{latestDate}</span>
              </div>
            </div>
          )}
        </div>

        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Calendar className="w-4 h-4" />
          <span className="text-xs text-muted-foreground/60">Navigate:</span>
          <button
            onClick={() => handleBreadcrumbClick(0)}
            className={`px-2 py-1 rounded-md transition-all duration-200 ${
              currentLevel.level === 0
                ? "text-foreground font-medium cursor-default bg-primary/10"
                : "hover:text-foreground hover:bg-muted/50 cursor-pointer"
            }`}
            disabled={currentLevel.level === 0}
          >
            Daily Task Sets
          </button>
          {currentLevel.level >= 1 && (
            <>
              <ChevronRight className="w-4 h-4" />
              <button
                onClick={() => handleBreadcrumbClick(1)}
                className={`px-2 py-1 rounded-md transition-all duration-200 ${
                  currentLevel.level === 1
                    ? "text-foreground font-medium cursor-default bg-primary/10"
                    : "hover:text-foreground hover:bg-muted/50 cursor-pointer"
                }`}
                disabled={currentLevel.level === 1}
              >
                {new Date(currentLevel.selectedDate!).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric'
                })} Breakdown
              </button>
            </>
          )}
          {currentLevel.level === 2 && (
            <>
              <ChevronRight className="w-4 h-4" />
              <span className="text-foreground font-medium">
                {formatColumnName(currentLevel.selectedGentype!)} Task Types
              </span>
            </>
          )}
        </div>
      </div>

      {/* Chart Container */}
      <div className="flex-1 flex items-end justify-center gap-4 p-6 min-h-[450px] bg-gradient-to-t from-muted/20 to-transparent rounded-lg overflow-x-auto">
        <AnimatePresence mode="wait">
          {dataWithPercentages.map((column: DailyColumnData, index: number) => {
            const barHeight = Math.min((column.value / maxValue) * 85, 85) // Limit to 85% of container height
            const hasBreakdown = (currentLevel.level === 0 && column.breakdown && column.breakdown.length > 0) ||
                                (currentLevel.level === 1 && column.taskTypes && column.taskTypes.length > 0)
            const isHovered = hoveredColumn === column.id

            return (
              <motion.div
                key={`${currentLevel.level}-${column.id}`}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -30, scale: 0.9 }}
                transition={{
                  delay: index * 0.02,
                  duration: 0.2,
                  type: "spring",
                  stiffness: 300,
                  damping: 25
                }}
                className="flex flex-col items-center group relative flex-shrink-0"
                onMouseEnter={() => setHoveredColumn(column.id)}
                onMouseLeave={() => setHoveredColumn(null)}
              >
                {/* Bar Column */}
                <div className="relative flex flex-col items-center">
                  <motion.div
                    className={`w-16 md:w-20 rounded-t-xl border-2 border-white/30 relative overflow-hidden shadow-lg ${
                      hasBreakdown ? 'cursor-pointer' : 'cursor-default'
                    }`}
                    style={{
                      height: `${Math.max(barHeight * 3.5, 60)}px`,
                      backgroundColor: column.color,
                      maxHeight: '350px',
                      boxShadow: `0 4px 20px ${column.color}40`
                    }}
                    onClick={() => handleColumnClick(column)}
                    whileHover={hasBreakdown ? {
                      scale: 1.05,
                      y: -5,
                      boxShadow: `0 8px 30px ${column.color}60`
                    } : {}}
                    whileTap={hasBreakdown ? { scale: 0.95 } : {}}
                    transition={{ duration: 0.1 }}
                  >
                    {/* Animated gradient overlay */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-white/10"
                      animate={isHovered ? { opacity: 0.8 } : { opacity: 1 }}
                      transition={{ duration: 0.15 }}
                    />
                    
                    {/* Shimmer effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                      animate={{ x: ['-100%', '100%'] }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        repeatDelay: 2,
                        ease: "easeInOut"
                      }}
                      style={{ width: '50%' }}
                    />
                    
                    {/* Value display */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.span 
                        className="text-white font-bold text-sm md:text-lg drop-shadow-lg"
                        animate={isHovered ? { scale: 1.1 } : { scale: 1 }}
                      >
                        {column.value}
                      </motion.span>
                    </div>

                    {/* Click indicator */}
                    {hasBreakdown && (
                      <motion.div 
                        className="absolute top-3 right-3"
                        animate={isHovered ? { scale: 1.2, rotate: 90 } : { scale: 1, rotate: 0 }}
                      >
                        <ChevronRight className="w-4 h-4 text-white drop-shadow-lg" />
                      </motion.div>
                    )}
                  </motion.div>

                  {/* Column Info */}
                  <motion.div 
                    className="mt-3 text-center space-y-1"
                    animate={isHovered ? { y: -2 } : { y: 0 }}
                  >
                    <div className="font-semibold text-sm text-foreground">
                      {formatColumnName(column.name)}
                    </div>
                    <div className="text-xs text-muted-foreground font-medium">
                      {column.percentage?.toFixed(1)}%
                    </div>
                    {hasBreakdown && (
                      <div className="text-xs text-blue-500 font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                        Click to explore
                      </div>
                    )}
                  </motion.div>
                </div>

                {/* Hover Tooltip */}
                <AnimatePresence>
                  {isHovered && (
                    <motion.div
                      initial={{ opacity: 0, x: -10, scale: 0.9 }}
                      animate={{ opacity: 1, x: 0, scale: 1 }}
                      exit={{ opacity: 0, x: -10, scale: 0.9 }}
                      transition={{ duration: 0.1 }}
                      className={`absolute ${
                        barHeight > 60
                          ? 'left-full top-1/2 transform -translate-y-1/2 ml-3'
                          : 'bottom-full left-1/2 transform -translate-x-1/2 mb-3'
                      } bg-background border border-border rounded-lg p-3 shadow-xl min-w-48 pointer-events-none`}
                      style={{
                        zIndex: 9999,
                        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                      }}
                    >
                      {/* Tooltip Arrow */}
                      {barHeight > 60 ? (
                        // Right-side arrow for tall bars
                        <>
                          <div
                            className="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-border"
                            style={{ marginRight: '-1px' }}
                          />
                          <div
                            className="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-background"
                            style={{ marginRight: '-2px' }}
                          />
                        </>
                      ) : (
                        // Bottom arrow for shorter bars
                        <>
                          <div
                            className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-border"
                            style={{ marginTop: '-1px' }}
                          />
                          <div
                            className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-background"
                            style={{ marginTop: '-2px' }}
                          />
                        </>
                      )}

                      <div className="space-y-2 text-sm">
                        <div className="font-medium flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: column.color }}
                          />
                          {formatColumnName(column.name)}
                          {column.date && (
                            <span className="text-xs text-muted-foreground">
                              ({new Date(column.date).toLocaleDateString()})
                            </span>
                          )}
                        </div>
                        <div className="space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              {currentLevel.level === 2 ? 'Count:' : 'Task Sets:'}
                            </span>
                            <span className="font-medium flex items-center gap-1">
                              <Target className="w-3 h-3 text-blue-500" />
                              {column.value.toLocaleString()}
                            </span>
                          </div>
                          {column.taskItems !== undefined && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Task Items:</span>
                              <span className="font-medium flex items-center gap-1">
                                <BarChart3 className="w-3 h-3 text-emerald-500" />
                                {column.taskItems.toLocaleString()}
                              </span>
                            </div>
                          )}
                          {column.avgSet !== undefined && column.avgSet > 0 && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Avg/Set:</span>
                              <span className="font-medium flex items-center gap-1">
                                <TrendingUp className="w-3 h-3 text-purple-500" />
                                {column.avgSet.toFixed(1)}
                              </span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Percentage:</span>
                            <span className="font-medium">{column.percentage?.toFixed(1)}%</span>
                          </div>
                        </div>
                        {hasBreakdown && (
                          <div className="pt-2 border-t border-border text-xs text-muted-foreground flex items-center gap-1">
                            <Info className="w-3 h-3" />
                            Click to see breakdown
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            )
          })}
        </AnimatePresence>
      </div>

      {/* Level Info */}
      <div className="text-center text-sm text-muted-foreground">
        {currentLevel.level === 0 ? (
          'Click on any day to see its task sets breakdown'
        ) : currentLevel.level === 1 ? (
          `Click on any gentype to see its task types breakdown • ${dataWithPercentages.length} gentypes`
        ) : (
          `Showing task types for ${formatColumnName(currentLevel.selectedGentype!)} • ${dataWithPercentages.length} task types`
        )}
      </div>
    </div>
  )
}

export default TaskSetsDailyInteractiveDrillDown
