import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import {
  <PERSON>rkles,
  Calendar,
  ArrowRight,
  Loader2,
  AlertCircle,
  Star,
  Crown,
  Zap,
  MessageCircle,
  BookOpen
} from 'lucide-react'
import { cn } from '../../../utils/cn'
import { CuratedService, EngagementQuestion } from '../../../services/curatedService'

interface TodaysThemeCardProps {
  cardVariants: any
}

interface TodaysTheme {
  theme_name: string
  theme_name_en: string
  theme_icon: string
  category: string
  font_color: string
  theme_color: string
  background_color: string
  theme_id: string
  engagement_questions?: EngagementQuestion[]
  description?: string
  description_en?: string
}

const TodaysThemeCard: React.FC<TodaysThemeCardProps> = ({ cardVariants }) => {
  const [theme, setTheme] = useState<TodaysTheme | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDarkTheme, setIsDarkTheme] = useState(() => {
    // Initialize from localStorage to persist theme preference
    const saved = localStorage.getItem('today-theme-dark')
    return saved !== null ? JSON.parse(saved) : false
  })
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const navigate = useNavigate()

  useEffect(() => {
    fetchTodaysTheme()
  }, [])

  // Auto slideshow for engagement questions
  useEffect(() => {
    if (theme?.engagement_questions && theme.engagement_questions.length > 1) {
      const interval = setInterval(() => {
        setCurrentQuestionIndex((prev) =>
          (prev + 1) % theme.engagement_questions!.length
        )
      }, 3500) // Change every 3.5 seconds

      return () => clearInterval(interval)
    }
  }, [theme?.engagement_questions])

  const fetchTodaysTheme = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await CuratedService.getTodaysTheme()
      setTheme(response.data)
    } catch (err) {
      console.error('Error fetching today\'s theme:', err)
      setError('Failed to load today\'s theme')
    } finally {
      setLoading(false)
    }
  }

  const handleNavigateToTasks = () => {
    if (theme?.task_set_id) {
      navigate(`/tasks/${theme.task_set_id}`)
    }
  }

  const handleBeginLearning = () => {
    // Navigate to existing begin-learning page (it will fetch today's theme automatically)
    navigate('/begin-learning')
  }

  const handleToggleTheme = () => {
    const newTheme = !isDarkTheme
    setIsDarkTheme(newTheme)
    // Persist the user's theme preference
    localStorage.setItem('today-theme-dark', JSON.stringify(newTheme))
  }

  const getFancyWords = () => {
    const words = [
      "Today's Featured Quest",
      "Daily Learning Adventure",
      "Today's Special Challenge",
      "Featured Theme of the Day",
      "Today's Learning Journey",
      "Daily Discovery Theme",
      "Today's Spotlight Challenge"
    ]
    return words[Math.floor(Math.random() * words.length)]
  }

  const getColorCombination = () => {
    if (!theme) return { primary: '#6366f1', secondary: '#8b5cf6' }

    if (isDarkTheme) {
      // Dark theme colors - deeper, more muted
      return {
        primary: '#1f2937', // gray-800
        secondary: '#374151'  // gray-700
      }
    } else {
      // Light theme colors - use theme color
      return {
        primary: theme.theme_color,
        secondary: theme.theme_color + 'CC'
      }
    }
  }

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 border border-border rounded-xl p-6"
      >
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-8 h-8 animate-spin text-purple-500" />
          <span className="ml-3 text-purple-600 dark:text-purple-400">
            Loading today's theme...
          </span>
        </div>
      </motion.div>
    )
  }

  if (error || !theme) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 border border-red-200 dark:border-red-800 rounded-xl p-6"
      >
        <div className="flex items-center justify-center py-8 text-red-600 dark:text-red-400">
          <AlertCircle className="w-6 h-6" />
          <span className="ml-3">{error || 'No theme available today'}</span>
        </div>
      </motion.div>
    )
  }

  const colors = getColorCombination()

  return (
    <motion.div
      variants={cardVariants}
      className={cn(
        "relative overflow-hidden rounded-xl border transition-all duration-300",
        isDarkTheme
          ? "border-gray-700 bg-gradient-to-br from-gray-800 to-gray-900"
          : "border-border bg-gradient-to-br from-white to-gray-50"
      )}
      style={{
        background: isDarkTheme
          ? `linear-gradient(135deg, ${colors.primary}25, ${colors.secondary}15)`
          : `linear-gradient(135deg, ${colors.primary}15, ${colors.primary}05)`
      }}
    >
      {/* Decorative background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div
          className={cn(
            "absolute -top-4 -right-4 w-20 h-20 rounded-full transition-opacity duration-300",
            isDarkTheme ? "opacity-20" : "opacity-10"
          )}
          style={{ backgroundColor: colors.primary }}
        />
        <div
          className={cn(
            "absolute -bottom-6 -left-6 w-28 h-28 rounded-full transition-opacity duration-300",
            isDarkTheme ? "opacity-15" : "opacity-5"
          )}
          style={{ backgroundColor: colors.secondary }}
        />
      </div>

      <div className="relative p-5">
        {/* Header with title and theme toggle - Better organized */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <motion.div
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Crown
                  className="w-5 h-5"
                  style={{ color: colors.primary }}
                />
              </motion.div>
              <h3 className={cn(
                "text-sm font-bold transition-colors duration-300",
                isDarkTheme ? "text-gray-200" : "text-foreground"
              )}>
                {getFancyWords()}
              </h3>
              <motion.div
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Sparkles
                  className="w-4 h-4"
                  style={{ color: colors.primary }}
                />
              </motion.div>
            </div>

            {/* Theme title with icon - More prominent */}
            <div className="flex items-center gap-3 mb-2">
              {theme.theme_icon && (
                <div
                  className="w-10 h-10 rounded-lg flex items-center justify-center text-xl shadow-sm"
                  style={{
                    backgroundColor: theme.background_color,
                    color: theme.font_color
                  }}
                >
                  {theme.theme_icon}
                </div>
              )}
              <div className="flex-1">
                <h4
                  className={cn(
                    "text-xl font-bold transition-colors duration-300",
                    isDarkTheme ? "text-white" : "text-gray-900"
                  )}
                  style={{ color: isDarkTheme ? '#ffffff' : colors.primary }}
                >
                  {theme.theme_name}
                </h4>
                {theme.theme_name_en && (
                  <p className="text-sm text-muted-foreground">
                    {theme.theme_name_en}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Calendar className="w-3 h-3" />
              <span className={isDarkTheme ? "text-gray-400" : "text-muted-foreground"}>
                Today's featured theme
              </span>
            </div>
          </div>

          {/* Theme Toggle Switch - Only changes appearance */}
          <div className="flex flex-col items-center gap-1">
            <motion.button
              onClick={handleToggleTheme}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300",
                isDarkTheme
                  ? "bg-gray-600"
                  : "bg-gradient-to-r from-blue-400 to-purple-500"
              )}
              aria-label={`Switch to ${isDarkTheme ? 'light' : 'dark'} theme`}
            >
              <motion.span
                animate={{
                  x: isDarkTheme ? 20 : 2
                }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
                className="inline-block h-4 w-4 transform rounded-full bg-white shadow-lg"
              />
            </motion.button>
            <span className={cn(
              "text-xs font-medium transition-colors duration-300",
              isDarkTheme ? "text-gray-400" : "text-gray-600"
            )}>
              {isDarkTheme ? 'Dark' : 'Light'}
            </span>
          </div>
        </div>

        {/* Engagement Questions Slideshow */}
        {theme.engagement_questions && theme.engagement_questions.length > 0 && (
          <div className="mb-4 p-4 bg-gradient-to-r from-emerald-100 via-teal-50 to-cyan-100 dark:from-emerald-900/40 dark:via-teal-900/30 dark:to-cyan-900/40 rounded-lg border-2 border-emerald-200/60 dark:border-emerald-700/50 shadow-lg">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg shadow-md">
                <MessageCircle className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <motion.p
                  key={currentQuestionIndex}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                  className="text-sm font-medium leading-relaxed text-emerald-800 dark:text-emerald-200"
                >
                  {theme.engagement_questions[currentQuestionIndex].text ||
                   theme.engagement_questions[currentQuestionIndex].text_en}
                </motion.p>
                {theme.engagement_questions.length > 1 && (
                  <div className="flex items-center justify-between mt-3">
                    <div className="flex gap-1.5">
                      {theme.engagement_questions.map((_, index) => (
                        <div
                          key={index}
                          className={cn(
                            'w-2 h-2 rounded-full transition-all duration-300',
                            index === currentQuestionIndex
                              ? 'bg-gradient-to-r from-emerald-500 to-teal-600 shadow-md scale-110'
                              : 'bg-emerald-300 dark:bg-emerald-600 hover:bg-emerald-400 dark:hover:bg-emerald-500'
                          )}
                        />
                      ))}
                    </div>
                    <span className="text-xs font-medium text-emerald-700 dark:text-emerald-300 bg-emerald-200/50 dark:bg-emerald-800/50 px-2 py-1 rounded-full">
                      {currentQuestionIndex + 1} of {theme.engagement_questions.length}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="space-y-2">
          {/* Begin Learning Button */}
          <motion.button
            onClick={handleBeginLearning}
            whileTap={{ scale: 0.98 }}
            className={cn(
              "w-full flex items-center justify-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200",
              isDarkTheme
                ? "text-white bg-gradient-to-r from-blue-600 to-purple-600"
                : "text-white"
            )}
            style={{
              background: isDarkTheme
                ? undefined
                : `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`
            }}
          >
            <BookOpen className="w-3.5 h-3.5" />
            <span>Begin Learning</span>
            <ArrowRight className="w-3.5 h-3.5" />
          </motion.button>

          {/* Original Challenge Button */}
          <motion.button
            onClick={handleNavigateToTasks}
            whileTap={{ scale: 0.98 }}
            className={cn(
              "w-full flex items-center justify-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 border",
              isDarkTheme
                ? "text-gray-300 border-gray-600 bg-gray-800/50 hover:bg-gray-700/50"
                : "text-gray-700 border-gray-300 bg-white/50 hover:bg-gray-50"
            )}
          >
            <Zap className="w-3.5 h-3.5" />
            <span>Start Challenge</span>
            <ArrowRight className="w-3.5 h-3.5" />
          </motion.button>
        </div>



        {/* Decorative stars - Positioned better */}
        <div className="absolute top-3 right-20">
          <motion.div
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Star
              className={cn(
                "w-3 h-3 transition-opacity duration-300",
                isDarkTheme ? "opacity-40" : "opacity-30"
              )}
              style={{ color: colors.primary }}
            />
          </motion.div>
        </div>
      </div>
    </motion.div>
  )
}

export default TodaysThemeCard
