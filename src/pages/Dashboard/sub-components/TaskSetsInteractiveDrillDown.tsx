import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowLeft, 
  ChevronRight, 
  Target, 
  BarChart3,
  TrendingUp,
  Info,
  Home
} from 'lucide-react'
import { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsInteractiveDrillDownProps {
  data: TaskSetsMetrics
}

interface DrillDownLevel {
  id: string
  name: string
  level: number
  path: string[]
}

interface ColumnData {
  id: string
  name: string
  value: number
  taskItems?: number
  color: string
  children?: ColumnData[]
  avgItemsPerSet?: number
  percentage?: number
}

const TaskSetsInteractiveDrillDown: React.FC<TaskSetsInteractiveDrillDownProps> = ({ data }) => {
  const [currentLevel, setCurrentLevel] = useState<DrillDownLevel>({
    id: 'root',
    name: 'Task Sets Distribution',
    level: 0,
    path: []
  })
  const [hoveredColumn, setHoveredColumn] = useState<string | null>(null)

  // Transform data for drill-down visualization
  const transformedData = useMemo(() => {
    // Use overall_sunburst_data if available, fallback to legacy sunburst_data
    const sunburstData = data?.overall_sunburst_data || data?.sunburst_data
    if (!sunburstData || sunburstData.length === 0) return []

    const transformNode = (node: any): ColumnData => {
      const totalValue = node.value || 0
      const taskItems = node.task_items_count || 0
      
      return {
        id: node.name,
        name: node.name,
        value: totalValue,
        taskItems,
        color: node.itemStyle?.color || '#6B7280',
        avgItemsPerSet: totalValue > 0 ? taskItems / totalValue : 0,
        children: node.children?.map((child: any) => transformNode(child)) || []
      }
    }

    // For root level, we want the main categories (skip the "Total Task Sets" wrapper if it exists)
    const rootData = sunburstData[0]
    if (rootData.name === 'Total Task Sets' && rootData.children) {
      return rootData.children.map(child => transformNode(child))
    }
    
    return sunburstData.map(node => transformNode(node))
  }, [data])

  // Get current level data
  const currentData = useMemo(() => {
    if (currentLevel.level === 0) {
      return transformedData
    }

    // Navigate to the current level
    let levelData = transformedData
    for (const pathSegment of currentLevel.path) {
      const found = levelData.find(item => item.id === pathSegment)
      if (found && found.children) {
        levelData = found.children
      } else {
        return []
      }
    }

    return levelData
  }, [transformedData, currentLevel])

  // Calculate percentages for current level
  const dataWithPercentages = useMemo(() => {
    const total = currentData.reduce((sum, item) => sum + item.value, 0)
    return currentData.map(item => ({
      ...item,
      percentage: total > 0 ? (item.value / total) * 100 : 0
    }))
  }, [currentData])

  // Get max value for scaling bars
  const maxValue = useMemo(() => {
    return Math.max(...currentData.map(item => item.value), 1)
  }, [currentData])

  const handleColumnClick = (column: ColumnData) => {
    if (column.children && column.children.length > 0) {
      setCurrentLevel({
        id: column.id,
        name: column.name,
        level: currentLevel.level + 1,
        path: [...currentLevel.path, column.id]
      })
    }
  }

  const handleBackClick = () => {
    if (currentLevel.level > 0) {
      const newPath = currentLevel.path.slice(0, -1)
      const parentName = newPath.length > 0 ? newPath[newPath.length - 1] : 'Task Sets Distribution'
      
      setCurrentLevel({
        id: newPath.length > 0 ? newPath[newPath.length - 1] : 'root',
        name: parentName,
        level: currentLevel.level - 1,
        path: newPath
      })
    }
  }

  const handleHomeClick = () => {
    setCurrentLevel({
      id: 'root',
      name: 'Task Sets Distribution',
      level: 0,
      path: []
    })
  }

  const formatColumnName = (name: string) => {
    return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  if (!data || dataWithPercentages.length === 0) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center space-y-2">
          <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto" />
          <p className="text-muted-foreground">No data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Navigation Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {currentLevel.level > 0 && (
            <button
              onClick={handleBackClick}
              className="flex items-center gap-2 px-3 py-1 rounded-lg bg-muted hover:bg-muted/80 transition-colors text-sm"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </button>
          )}
          {currentLevel.level > 1 && (
            <button
              onClick={handleHomeClick}
              className="flex items-center gap-2 px-3 py-1 rounded-lg bg-muted hover:bg-muted/80 transition-colors text-sm"
            >
              <Home className="w-4 h-4" />
              Home
            </button>
          )}
        </div>
        
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Task Sets</span>
          {currentLevel.path.map((segment, index) => (
            <React.Fragment key={segment}>
              <ChevronRight className="w-4 h-4" />
              <span className="text-foreground font-medium">
                {formatColumnName(segment)}
              </span>
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Chart Container */}
      <div className="flex-1 flex items-end justify-center gap-6 p-6 min-h-0 bg-gradient-to-t from-muted/20 to-transparent rounded-lg">
        <AnimatePresence mode="wait">
          {dataWithPercentages.map((column, index) => {
            const barHeight = (column.value / maxValue) * 100
            const hasChildren = column.children && column.children.length > 0
            const isHovered = hoveredColumn === column.id

            return (
              <motion.div
                key={`${currentLevel.level}-${column.id}`}
                initial={{ opacity: 0, y: 50, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -50, scale: 0.8 }}
                transition={{
                  delay: index * 0.1,
                  duration: 0.6,
                  type: "spring",
                  stiffness: 100
                }}
                className="flex flex-col items-center group relative"
                onMouseEnter={() => setHoveredColumn(column.id)}
                onMouseLeave={() => setHoveredColumn(null)}
              >
                {/* Bar Column */}
                <div className="relative flex flex-col items-center">
                  <motion.div
                    className={`w-20 md:w-24 rounded-t-xl border-2 border-white/30 relative overflow-hidden shadow-lg ${
                      hasChildren ? 'cursor-pointer' : 'cursor-default'
                    }`}
                    style={{
                      height: `${Math.max(barHeight * 3, 50)}px`,
                      backgroundColor: column.color,
                      maxHeight: '320px',
                      boxShadow: `0 4px 20px ${column.color}40`
                    }}
                    onClick={() => handleColumnClick(column)}
                    whileHover={hasChildren ? {
                      scale: 1.08,
                      y: -8,
                      boxShadow: `0 8px 30px ${column.color}60`
                    } : {}}
                    whileTap={hasChildren ? { scale: 0.95 } : {}}
                  >
                    {/* Animated gradient overlay */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-white/10"
                      animate={isHovered ? { opacity: 0.8 } : { opacity: 1 }}
                    />

                    {/* Shimmer effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                      animate={{ x: ['-100%', '100%'] }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        repeatDelay: 3,
                        ease: "easeInOut"
                      }}
                      style={{ width: '50%' }}
                    />

                    {/* Value display */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.span
                        className="text-white font-bold text-sm md:text-lg drop-shadow-lg"
                        animate={isHovered ? { scale: 1.1 } : { scale: 1 }}
                      >
                        {column.value}
                      </motion.span>
                    </div>

                    {/* Click indicator */}
                    {hasChildren && (
                      <motion.div
                        className="absolute top-3 right-3"
                        animate={isHovered ? { scale: 1.2, rotate: 90 } : { scale: 1, rotate: 0 }}
                      >
                        <ChevronRight className="w-4 h-4 text-white drop-shadow-lg" />
                      </motion.div>
                    )}
                  </motion.div>

                  {/* Column Info */}
                  <motion.div
                    className="mt-3 text-center space-y-1"
                    animate={isHovered ? { y: -2 } : { y: 0 }}
                  >
                    <div className="font-semibold text-sm text-foreground">
                      {formatColumnName(column.name)}
                    </div>
                    <div className="text-xs text-muted-foreground font-medium">
                      {column.percentage?.toFixed(1)}%
                    </div>
                    {hasChildren && (
                      <div className="text-xs text-blue-500 font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                        Click to explore
                      </div>
                    )}
                  </motion.div>
                </div>

                {/* Hover Tooltip */}
                <AnimatePresence>
                  {isHovered && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.9 }}
                      className={`fixed bg-background border border-border rounded-lg p-3 shadow-xl min-w-48 pointer-events-none`}
                      style={{
                        zIndex: 9999,
                        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                        left: index > dataWithPercentages.length / 2 ? 'auto' : '100%',
                        right: index > dataWithPercentages.length / 2 ? '100%' : 'auto',
                        bottom: '100%',
                        transform: index > dataWithPercentages.length / 2 ? 'translateX(100%)' : 'translateX(-100%)',
                        marginLeft: index > dataWithPercentages.length / 2 ? '-12px' : '12px',
                        marginBottom: '12px'
                      }}
                    >
                      {/* Tooltip Arrow */}
                      <div
                        className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-border"
                        style={{ marginTop: '-1px' }}
                      />
                      <div
                        className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-background"
                        style={{ marginTop: '-2px' }}
                      />

                      <div className="space-y-2 text-sm">
                        <div className="font-medium flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: column.color }}
                          />
                          {formatColumnName(column.name)}
                        </div>
                        <div className="space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Task Sets:</span>
                            <span className="font-medium flex items-center gap-1">
                              <Target className="w-3 h-3 text-blue-500" />
                              {column.value.toLocaleString()}
                            </span>
                          </div>
                          {column.taskItems !== undefined && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Task Items:</span>
                              <span className="font-medium flex items-center gap-1">
                                <BarChart3 className="w-3 h-3 text-emerald-500" />
                                {column.taskItems.toLocaleString()}
                              </span>
                            </div>
                          )}
                          {column.avgItemsPerSet !== undefined && column.avgItemsPerSet > 0 && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Avg/Set:</span>
                              <span className="font-medium flex items-center gap-1">
                                <TrendingUp className="w-3 h-3 text-purple-500" />
                                {column.avgItemsPerSet.toFixed(1)}
                              </span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Percentage:</span>
                            <span className="font-medium">{column.percentage?.toFixed(1)}%</span>
                          </div>
                        </div>
                        {hasChildren && (
                          <div className="pt-2 border-t border-border text-xs text-muted-foreground flex items-center gap-1">
                            <Info className="w-3 h-3" />
                            Click to drill down
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            )
          })}
        </AnimatePresence>
      </div>

      {/* Level Info */}
      <div className="text-center text-sm text-muted-foreground">
        {currentLevel.level === 0 ? (
          'Click on any column to drill down into details'
        ) : (
          `Showing ${formatColumnName(currentLevel.name)} breakdown • ${dataWithPercentages.length} categories`
        )}
      </div>
    </div>
  )
}

export default TaskSetsInteractiveDrillDown
