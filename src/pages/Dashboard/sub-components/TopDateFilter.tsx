import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Calendar, Filter, RotateCcw, ChevronDown, RefreshCw } from 'lucide-react'
import { cn } from '../../../utils/cn'

interface TopDateFilterProps {
  startDate: string
  endDate: string
  onDateChange: (startDate: string, endDate: string) => void
  onReset: () => void
  onRefreshAll: () => void
  isRefreshing?: boolean
  lastUpdated?: string
  cardVariants: any
}

/**
 * Top Date Filter - Single line date filter for dashboard
 * Professional design matching the reference image
 */
const TopDateFilter: React.FC<TopDateFilterProps> = ({
  startDate,
  endDate,
  onDateChange,
  onReset,
  onRefreshAll,
  isRefreshing = false,
  lastUpdated,
  cardVariants
}) => {
  const [showCustom, setShowCustom] = useState(false)

  // Preset date ranges
  const presets = [
    {
      label: 'Today',
      getValue: () => {
        const today = new Date().toISOString().split('T')[0]
        return { start: today, end: today }
      }
    },
    {
      label: 'Yesterday',
      getValue: () => {
        const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        return { start: yesterday, end: yesterday }
      }
    },
    {
      label: 'Last 7 Days',
      getValue: () => {
        const end = new Date().toISOString().split('T')[0]
        const start = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        return { start, end }
      }
    },
    {
      label: 'Last 30 Days',
      getValue: () => {
        const end = new Date().toISOString().split('T')[0]
        const start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        return { start, end }
      }
    },
    {
      label: 'This Month',
      getValue: () => {
        const now = new Date()
        const start = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
        const end = new Date().toISOString().split('T')[0]
        return { start, end }
      }
    }
  ]

  const handlePresetClick = (preset: typeof presets[0]) => {
    const { start, end } = preset.getValue()
    onDateChange(start, end)
    setShowCustom(false)
  }

  const handleCustomDateChange = (field: 'start' | 'end', value: string) => {
    if (field === 'start') {
      onDateChange(value, endDate)
    } else {
      onDateChange(startDate, value)
    }
  }

  const isPresetActive = (preset: typeof presets[0]) => {
    const { start, end } = preset.getValue()
    return startDate === start && endDate === end
  }

  const formatDateRange = () => {
    const start = new Date(startDate).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
    const end = new Date(endDate).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    })
    return `${start} - ${end}`
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-white/90 via-blue-50/60 to-purple-50/70 dark:from-gray-900/90 dark:via-blue-900/30 dark:to-purple-900/40 border border-blue-200/60 dark:border-blue-800/40 rounded-2xl p-6 relative overflow-hidden shadow-xl backdrop-blur-sm"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-50/20 to-indigo-100/30 dark:from-blue-900/20 dark:via-purple-900/10 dark:to-indigo-900/20 rounded-2xl animate-pulse" style={{ animationDuration: '4s' }} />

      <div className="relative space-y-4">
        {/* Top Row: Title and Actions */}
        <div className="flex items-center justify-between">
          {/* Left side - Title and current range */}
          <div className="flex items-center gap-2">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Filter className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-foreground">Date Range</h3>
              <p className="text-xs text-muted-foreground">{formatDateRange()}</p>
            </div>
          </div>

          {/* Right side - Action buttons */}
          <div className="flex items-center gap-2">
            {/* Global Refresh Button */}
            <motion.button
              onClick={onRefreshAll}
              disabled={isRefreshing}
              whileHover={!isRefreshing ? { scale: 1.05 } : {}}
              whileTap={!isRefreshing ? { scale: 0.95 } : {}}
              className={cn(
                "flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-sm font-medium border",
                isRefreshing
                  ? "bg-muted text-muted-foreground border-border cursor-not-allowed"
                  : "bg-primary/10 hover:bg-primary/20 text-primary border-primary/20 hover:border-primary/30"
              )}
              title={isRefreshing ? "Refreshing..." : "Refresh all dashboard data"}
            >
              <RefreshCw className={cn(
                "h-4 w-4 transition-transform duration-200",
                isRefreshing && "animate-spin"
              )} />
              {isRefreshing ? "Refreshing..." : "Refresh All"}
            </motion.button>

            {/* Reset Date Range Button */}
            <motion.button
              onClick={onReset}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 hover:bg-muted rounded-md transition-colors"
              title="Reset to default date range"
            >
              <RotateCcw className="h-4 w-4 text-muted-foreground" />
            </motion.button>
          </div>
        </div>

        {/* Bottom Row: Presets and Last Updated */}
        <div className="flex items-center justify-between flex-wrap gap-4">

          {/* Left side - Preset buttons */}
          <div className="flex items-center gap-2 flex-wrap">
            {presets.map((preset) => (
              <motion.button
                key={preset.label}
                onClick={() => handlePresetClick(preset)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={cn(
                  "px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200",
                  isPresetActive(preset)
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground"
                )}
              >
                {preset.label}
              </motion.button>
            ))}

            {/* Custom Range Toggle */}
            <motion.button
              onClick={() => setShowCustom(!showCustom)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={cn(
                "px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 flex items-center gap-1",
                showCustom
                  ? "bg-secondary text-secondary-foreground"
                  : "bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground"
              )}
            >
              <Calendar className="h-3 w-3" />
              Custom
              <ChevronDown className={cn(
                "h-3 w-3 transition-transform duration-200",
                showCustom && "rotate-180"
              )} />
            </motion.button>
          </div>

          {/* Right side - Last Updated Info */}
          {lastUpdated && (
            <div className="flex items-center gap-2 px-3 py-2 bg-muted/50 rounded-lg">
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
              <div className="text-xs text-muted-foreground">
                <span className="font-medium">Last updated:</span> {new Date(lastUpdated).toLocaleString()}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Custom Date Inputs */}
      <motion.div
        initial={false}
        animate={{ 
          height: showCustom ? 'auto' : 0, 
          opacity: showCustom ? 1 : 0,
          marginTop: showCustom ? 16 : 0
        }}
        transition={{ duration: 0.2 }}
        className="overflow-hidden"
      >
        <div className="flex items-center gap-4 pt-4 border-t border-border">
          <div className="flex items-center gap-2">
            <label className="text-xs font-medium text-muted-foreground">
              From:
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => handleCustomDateChange('start', e.target.value)}
              className="px-3 py-1.5 text-xs bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
          </div>
          <div className="flex items-center gap-2">
            <label className="text-xs font-medium text-muted-foreground">
              To:
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => handleCustomDateChange('end', e.target.value)}
              className="px-3 py-1.5 text-xs bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default TopDateFilter
