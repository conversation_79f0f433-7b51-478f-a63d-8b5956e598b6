import React from 'react'
import { motion } from 'framer-motion'
import { Users, UserPlus, Activity, FileText, Plus, CheckCircle, TrendingUp, RefreshCw } from 'lucide-react'
import { cn } from '../../../utils/cn'
import type { DashboardOverview } from '../../../services/management/managementService'

// Compact card animations with proper overflow handling
const modernCardStyles = `
  .modern-card {
    transition: all 0.2s ease;
    border-width: 1px;
    border-style: solid;
    contain: layout style;
    position: relative;
    z-index: 1;
  }

  .modern-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10;
    transform: translateY(-1px);
  }
`

interface OverviewMetricsCardProps {
  data: DashboardOverview | null
  taskSetsData: any | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

/**
 * Overview Metrics Card - Displays key dashboard metrics
 * Sleek design with animated counters and hover effects
 */
const OverviewMetricsCard: React.FC<OverviewMetricsCardProps> = ({
  data,
  taskSetsData,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
      >
        {/* Animated loading background */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: i * 0.1 }}
                className="bg-background border border-border rounded-lg p-4 space-y-3"
              >
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 bg-muted rounded-lg animate-pulse" />
                </div>
                <div className="space-y-2">
                  <div className="h-6 bg-muted rounded w-12 animate-pulse" />
                  <div className="h-3 bg-muted rounded w-20 animate-pulse" />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load metrics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) return null

  const metrics = [
    {
      label: 'Total Users',
      value: data.overview.total_users,
      icon: Users,
      borderColor: 'border-blue-500',
      bgColor: 'bg-blue-500/5 dark:bg-blue-500/10',
      iconColor: 'text-blue-500',
      textColor: 'text-blue-600 dark:text-blue-400'
    },
    {
      label: 'Joined Today',
      value: data.overview.users_joined_today,
      icon: UserPlus,
      borderColor: 'border-emerald-500',
      bgColor: 'bg-emerald-500/5 dark:bg-emerald-500/10',
      iconColor: 'text-emerald-500',
      textColor: 'text-emerald-600 dark:text-emerald-400'
    },
    {
      label: 'Active Today',
      value: data.overview.users_active_today,
      icon: Activity,
      borderColor: 'border-purple-500',
      bgColor: 'bg-purple-500/5 dark:bg-purple-500/10',
      iconColor: 'text-purple-500',
      textColor: 'text-purple-600 dark:text-purple-400'
    },
    {
      label: 'Total Task Sets',
      value: data.overview.total_task_sets,
      icon: FileText,
      borderColor: 'border-orange-500',
      bgColor: 'bg-orange-500/5 dark:bg-orange-500/10',
      iconColor: 'text-orange-500',
      textColor: 'text-orange-600 dark:text-orange-400'
    },
    {
      label: 'Created Today',
      value: data.overview.task_sets_created_today,
      icon: Plus,
      borderColor: 'border-teal-500',
      bgColor: 'bg-teal-500/5 dark:bg-teal-500/10',
      iconColor: 'text-teal-500',
      textColor: 'text-teal-600 dark:text-teal-400'
    },
    {
      label: 'Completed Today',
      value: data.overview.task_sets_completed_today,
      icon: CheckCircle,
      borderColor: 'border-green-500',
      bgColor: 'bg-green-500/5 dark:bg-green-500/10',
      iconColor: 'text-green-500',
      textColor: 'text-green-600 dark:text-green-400'
    }
  ]

  // Compact MetricCard component with proper sizing
  const MetricCard = ({ metric, index }: { metric: any, index: number }) => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        delay: index * 0.05,
        duration: 0.3,
        ease: "easeOut"
      }}
      whileHover={{
        scale: 1.02,
        transition: { duration: 0.2 }
      }}
      className={cn(
        "modern-card rounded-lg p-2.5 h-full cursor-pointer relative border transition-all duration-200 ease-in-out",
        metric.borderColor,
        metric.bgColor,
        // Add hover border color classes
        `hover:${metric.borderColor}`
      )}
      style={{
        minHeight: '80px',
        maxWidth: '100%'
      } as React.CSSProperties}
    >
      {/* Header with icon and label */}
      <div className="flex items-center gap-1.5 mb-1.5">
        <div className={cn("p-0.5 rounded", metric.iconColor)}>
          <metric.icon className="h-3 w-3" />
        </div>
        <span className="text-xs font-medium text-gray-600 dark:text-gray-400 truncate leading-tight">
          {metric.label}
        </span>
      </div>

      {/* Compact number display */}
      <div className={cn("text-lg font-bold leading-tight", metric.textColor)}>
        {metric.value.toLocaleString()}
      </div>
    </motion.div>
  )

  return (
    <>
      <style>{modernCardStyles}</style>
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6"
      >
        {/* Header with title and refresh button */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <TrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-foreground">Overview Metrics</h3>
              <p className="text-xs text-muted-foreground">Key dashboard statistics</p>
            </div>
          </div>

          {/* Refresh button in top-right */}
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-muted rounded-lg transition-colors"
            title="Refresh overview metrics"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </motion.button>
        </div>

        {/* Metrics Grid - 3x2 layout for 6 cards with proper spacing */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {metrics.map((metric, index) => (
            <MetricCard key={metric.label} metric={metric} index={index} />
          ))}
        </div>
      </motion.div>
    </>
  )
}

export default OverviewMetricsCard
