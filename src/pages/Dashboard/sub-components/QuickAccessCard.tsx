import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import {
  Mi<PERSON>,
  Clock,
  ChevronRight,
  Headphones,
  Target,
  Zap,
  BookOpen,
  BarChart2,
  Award
} from 'lucide-react'
import { cn } from '../../../utils/cn'

interface QuickAccessCardProps {
  cardVariants: any
}

const QuickAccessCard: React.FC<QuickAccessCardProps> = ({ cardVariants }) => {
  const quickAccessItems = [
    {
      title: 'Begin Learning',
      description: 'Start your Nepali learning session with interactive audio exercises and real-time pronunciation feedback.',
      icon: Mic,
      iconBg: 'bg-gradient-to-br from-green-500 to-emerald-600',
      path: '/begin-learning',
      badge: 'Interactive',
      badgeColor: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
    },
    {
      title: 'Playground History',
      description: 'Review your completed tasks, track your progress, and see detailed performance analytics.',
      icon: Clock,
      iconBg: 'bg-gradient-to-br from-blue-500 to-indigo-600',
      path: '/tasks',
      badge: 'Progress',
      badgeColor: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
    }
  ] 

  return (
    <motion.div
      variants={cardVariants}
      className="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden h-full flex flex-col"
    >
      <div className="p-3 sm:p-4 border-b border-slate-200 dark:border-slate-700">
        <h3 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center gap-2">
          <Zap className="h-5 w-5 text-amber-500" />
          Quick Access
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 p-3 sm:p-4 flex-1 overflow-auto">
        {quickAccessItems.map((item, index) => (
          <Link to={item.path} key={index}>
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="bg-slate-50 dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700 rounded-lg p-3 sm:p-4 h-full hover:shadow-md transition-all duration-200"
            >
              <div className="flex items-start gap-3 sm:gap-4">
                <div className={cn(
                  "p-2 sm:p-2.5 rounded-lg text-white flex-shrink-0",
                  item.iconBg
                )}>
                  <item.icon className="h-5 w-5" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-slate-900 dark:text-white text-base">{item.title}</h4>
                    <span className={cn(
                      "text-xs px-2 py-0.5 rounded-full font-medium",
                      item.badgeColor
                    )}>
                      {item.badge}
                    </span>
                  </div>
                  
                  <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-2 mb-2">
                    {item.description}
                  </p>
                  
                  <div className="flex items-center text-sm font-medium text-blue-600 dark:text-blue-400">
                    Start now
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </div>
                </div>
              </div>
            </motion.div>
          </Link>
        ))}
      </div>

      <div className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800/30 dark:to-slate-800/10 p-3 border-t border-slate-200 dark:border-slate-700">
        <div className="grid grid-cols-3 gap-2 sm:gap-3">
          <div className="flex flex-col items-center p-2 rounded-lg hover:bg-white dark:hover:bg-slate-800 transition-colors">
            <div className="p-1.5 bg-purple-100 dark:bg-purple-900/20 rounded-full mb-1.5">
              <Headphones className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
            <span className="text-xs text-slate-700 dark:text-slate-300 text-center">Audio Learning</span>
          </div>
          
          <div className="flex flex-col items-center p-2 rounded-lg hover:bg-white dark:hover:bg-slate-800 transition-colors">
            <div className="p-1.5 bg-amber-100 dark:bg-amber-900/20 rounded-full mb-1.5">
              <Target className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            </div>
            <span className="text-xs text-slate-700 dark:text-slate-300 text-center">Adaptive Tasks</span>
          </div>
          
          <div className="flex flex-col items-center p-2 rounded-lg hover:bg-white dark:hover:bg-slate-800 transition-colors">
            <div className="p-1.5 bg-green-100 dark:bg-green-900/20 rounded-full mb-1.5">
              <BookOpen className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            <span className="text-xs text-slate-700 dark:text-slate-300 text-center">Personalized</span>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default QuickAccessCard