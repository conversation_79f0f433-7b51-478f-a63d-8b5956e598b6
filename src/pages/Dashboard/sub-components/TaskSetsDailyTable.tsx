import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ChevronDown, 
  ChevronRight, 
  Calendar, 
  Target, 
  BarChart3,
  TrendingUp,
  Search,
  SortAsc,
  SortDesc,
  Info
} from 'lucide-react'
import { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsDailyTableProps {
  data: TaskSetsMetrics
}

interface DailyTableRow {
  id: string
  date: string
  taskSets: number
  taskItems: number
  avgItemsPerSet: number
  isExpanded?: boolean
  gentypeBreakdown: Array<{
    gentype: string
    task_sets_count: number
    task_items_count: number
    task_types: Array<{
      task_type: string
      count: number
    }>
  }>
}

interface ExpandedRowData {
  gentype: string
  taskSets: number
  taskItems: number
  taskTypes: Array<{
    task_type: string
    count: number
  }>
}

type SortField = 'date' | 'taskSets' | 'taskItems' | 'avgItemsPerSet'
type SortDirection = 'asc' | 'desc'

const TaskSetsDailyTable: React.FC<TaskSetsDailyTableProps> = ({ data }) => {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState<SortField>('date')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')

  // Transform daily data to table rows
  const tableData = useMemo(() => {
    if (!data?.daily_data || data.daily_data.length === 0) return []

    return data.daily_data.map(dayData => ({
      id: dayData.date,
      date: dayData.date,
      taskSets: dayData.total_task_sets,
      taskItems: dayData.total_task_items,
      avgItemsPerSet: dayData.total_task_sets > 0 ? dayData.total_task_items / dayData.total_task_sets : 0,
      gentypeBreakdown: dayData.gentype_breakdown
    }))
  }, [data])

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    let filtered = tableData.filter(row => 
      row.date.includes(searchTerm) || 
      row.gentypeBreakdown.some(gentype => 
        gentype.gentype.toLowerCase().includes(searchTerm.toLowerCase())
      )
    )

    // Sort data
    filtered.sort((a, b) => {
      let aValue: number | string = a[sortField]
      let bValue: number | string = b[sortField]

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = (bValue as string).toLowerCase()
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

    return filtered
  }, [tableData, searchTerm, sortField, sortDirection])

  const toggleExpanded = (rowId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev)
      if (newSet.has(rowId)) {
        newSet.delete(rowId)
      } else {
        newSet.add(rowId)
      }
      return newSet
    })
  }

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return null
    return sortDirection === 'asc' ? 
      <SortAsc className="w-4 h-4" /> : 
      <SortDesc className="w-4 h-4" />
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      weekday: 'short'
    })
  }

  const getGentypeColor = (gentype: string) => {
    const colors: { [key: string]: string } = {
      'primary': '#F54F4A',
      'curated': '#FF8C75', 
      'follow_up': '#FFB499'
    }
    return colors[gentype] || '#6B7280'
  }

  if (!data?.daily_data || data.daily_data.length === 0) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center space-y-2">
          <Calendar className="h-12 w-12 text-muted-foreground mx-auto" />
          <p className="text-muted-foreground">No daily data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Search and Controls */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search dates or generation types..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
          />
        </div>
        <div className="text-sm text-muted-foreground">
          {filteredAndSortedData.length} days
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-auto border border-border rounded-lg bg-background">
        <table className="w-full">
          <thead className="bg-muted/50 sticky top-0 z-10">
            <tr>
              <th className="text-left p-3 font-medium text-foreground w-8"></th>
              <th className="text-left p-3 font-medium text-foreground">
                <button
                  onClick={() => handleSort('date')}
                  className="flex items-center gap-2 hover:text-primary transition-colors"
                >
                  Date
                  {getSortIcon('date')}
                </button>
              </th>
              <th className="text-right p-3 font-medium text-foreground">
                <button
                  onClick={() => handleSort('taskSets')}
                  className="flex items-center gap-2 hover:text-primary transition-colors ml-auto"
                >
                  Task Sets
                  {getSortIcon('taskSets')}
                </button>
              </th>
              <th className="text-right p-3 font-medium text-foreground">
                <button
                  onClick={() => handleSort('taskItems')}
                  className="flex items-center gap-2 hover:text-primary transition-colors ml-auto"
                >
                  Task Items
                  {getSortIcon('taskItems')}
                </button>
              </th>
              <th className="text-right p-3 font-medium text-foreground">
                <button
                  onClick={() => handleSort('avgItemsPerSet')}
                  className="flex items-center gap-2 hover:text-primary transition-colors ml-auto"
                >
                  Avg/Set
                  {getSortIcon('avgItemsPerSet')}
                </button>
              </th>
            </tr>
          </thead>
          <tbody>
            <AnimatePresence>
              {filteredAndSortedData.map((row, index) => {
                const isExpanded = expandedRows.has(row.id)
                
                return (
                  <React.Fragment key={row.id}>
                    {/* Main Row */}
                    <motion.tr
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ delay: index * 0.02 }}
                      className="border-b border-border hover:bg-muted/30 transition-colors group"
                    >
                      <td className="p-3">
                        <button
                          onClick={() => toggleExpanded(row.id)}
                          className="p-1 hover:bg-muted rounded transition-colors"
                        >
                          {isExpanded ? (
                            <ChevronDown className="w-4 h-4" />
                          ) : (
                            <ChevronRight className="w-4 h-4" />
                          )}
                        </button>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-blue-500 opacity-60" />
                          <span className="font-medium text-foreground">
                            {formatDate(row.date)}
                          </span>
                        </div>
                      </td>
                      <td className="p-3 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <span className="font-semibold text-blue-600 dark:text-blue-400">
                            {row.taskSets.toLocaleString()}
                          </span>
                          <Target className="w-4 h-4 text-blue-500 opacity-60" />
                        </div>
                      </td>
                      <td className="p-3 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <span className="font-semibold text-emerald-600 dark:text-emerald-400">
                            {row.taskItems.toLocaleString()}
                          </span>
                          <BarChart3 className="w-4 h-4 text-emerald-500 opacity-60" />
                        </div>
                      </td>
                      <td className="p-3 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <span className="font-semibold text-purple-600 dark:text-purple-400">
                            {row.avgItemsPerSet.toFixed(1)}
                          </span>
                          <TrendingUp className="w-4 h-4 text-purple-500 opacity-60" />
                        </div>
                      </td>
                    </motion.tr>

                    {/* Expanded Content */}
                    {isExpanded && (
                      <motion.tr
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="bg-muted/20"
                      >
                        <td colSpan={5} className="p-0">
                          <div className="p-4 space-y-3">
                            <h4 className="font-medium text-sm text-muted-foreground mb-3">
                              Generation Type Breakdown
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              {row.gentypeBreakdown.map((gentype) => (
                                <div
                                  key={gentype.gentype}
                                  className="bg-background border border-border rounded-lg p-3 space-y-2"
                                >
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-3 h-3 rounded-full"
                                      style={{ backgroundColor: getGentypeColor(gentype.gentype) }}
                                    />
                                    <span className="font-medium text-sm capitalize">
                                      {gentype.gentype.replace('_', ' ')}
                                    </span>
                                  </div>
                                  <div className="text-xs space-y-1">
                                    <div className="flex justify-between">
                                      <span className="text-muted-foreground">Sets:</span>
                                      <span className="font-medium">{gentype.task_sets_count}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-muted-foreground">Items:</span>
                                      <span className="font-medium">{gentype.task_items_count}</span>
                                    </div>
                                  </div>
                                  <div className="pt-2 border-t border-border">
                                    <div className="text-xs text-muted-foreground mb-1">Task Types:</div>
                                    <div className="flex flex-wrap gap-1">
                                      {gentype.task_types.map((taskType) => (
                                        <span
                                          key={taskType.task_type}
                                          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-muted text-muted-foreground"
                                        >
                                          {taskType.task_type.replace('_', ' ')}: {taskType.count}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </td>
                      </motion.tr>
                    )}
                  </React.Fragment>
                )
              })}
            </AnimatePresence>
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default TaskSetsDailyTable
