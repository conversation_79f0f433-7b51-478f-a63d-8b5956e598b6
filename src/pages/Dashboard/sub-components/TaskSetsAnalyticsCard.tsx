import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, Table, Database, Calendar, BarChart3 } from 'lucide-react'
import { TaskSetsMetrics } from '../../../services/management/managementService'
import TaskSetsInteractiveDrillDown from './TaskSetsInteractiveDrillDown'
import TaskSetsDailyInteractiveDrillDown from './TaskSetsDailyInteractiveDrillDown'

interface TaskSetsAnalyticsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

// Interactive Table Component for Task Sets Distribution

/**
 * Task Sets Analytics Card - Clean bar chart visualization
 */
const TaskSetsAnalyticsCard: React.FC<TaskSetsAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  const [viewMode, setViewMode] = useState<'daily' | 'overall'>('daily')

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="flex-1 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <Table className="h-5 w-5" />
                Task Sets Distribution
              </h3>
              <p className="text-sm text-muted-foreground">Interactive table view</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <p className="text-destructive">Failed to load chart data</p>
              <button
                onClick={onRefresh}
                className="text-sm text-primary hover:underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  if (!data) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <Table className="h-5 w-5" />
                Task Sets Distribution
              </h3>
              <p className="text-sm text-muted-foreground">Interactive table view</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <Database className="h-12 w-12 text-muted-foreground mx-auto" />
              <p className="text-muted-foreground">No data available</p>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-white/90 via-blue-50/60 to-purple-50/70 dark:from-gray-900/90 dark:via-blue-900/30 dark:to-purple-900/40 border border-blue-200/60 dark:border-blue-800/40 rounded-2xl p-6 relative overflow-hidden h-full shadow-xl backdrop-blur-sm"
    >
      {/* Enhanced Background gradient with animation */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-50/20 to-indigo-100/30 dark:from-blue-900/20 dark:via-purple-900/10 dark:to-indigo-900/20 rounded-2xl animate-pulse" style={{ animationDuration: '4s' }} />

      <div className="relative space-y-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              {viewMode === 'daily' ? (
                <Calendar className="h-5 w-5 text-blue-600" />
              ) : (
                <BarChart3 className="h-5 w-5 text-blue-600" />
              )}
              Task Sets Distribution
            </h3>
            <p className="text-sm text-muted-foreground">
              {viewMode === 'daily' ? 'Daily breakdown view' : 'Interactive drill-down visualization'}
            </p>
          </div>
          <div className="flex items-center gap-2">
            {/* View Toggle */}
            <div className="flex items-center bg-muted rounded-lg p-1">
              <button
                onClick={() => setViewMode('daily')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'daily'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                <Calendar className="w-4 h-4 mr-1 inline" />
                Daily
              </button>
              <button
                onClick={() => setViewMode('overall')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'overall'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                <BarChart3 className="w-4 h-4 mr-1 inline" />
                Overall
              </button>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors group"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground group-hover:rotate-180 transition-transform duration-500" />
            </button>
          </div>
        </div>

        {/* Content Container - Optimized size */}
        <div className="flex-1 min-h-0">
          {viewMode === 'daily' ? (
            <TaskSetsDailyInteractiveDrillDown data={data} />
          ) : (
            <TaskSetsInteractiveDrillDown data={data} />
          )}
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsAnalyticsCard