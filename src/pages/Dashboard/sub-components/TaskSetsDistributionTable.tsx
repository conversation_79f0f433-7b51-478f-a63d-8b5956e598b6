import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ChevronDown, 
  ChevronRight, 
  Target, 
  Database, 
  BarChart3,
  TrendingUp,
  Info,
  Search,
  SortAsc,
  SortDesc
} from 'lucide-react'
import { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsDistributionTableProps {
  data: TaskSetsMetrics
}

interface TableRow {
  id: string
  name: string
  level: number
  taskSets: number
  taskItems: number
  avgItemsPerSet: number
  color: string
  parentId?: string
  isExpanded?: boolean
  children?: TableRow[]
}

type SortField = 'name' | 'taskSets' | 'taskItems' | 'avgItemsPerSet'
type SortDirection = 'asc' | 'desc'

const TaskSetsDistributionTable: React.FC<TaskSetsDistributionTableProps> = ({ data }) => {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState<SortField>('taskSets')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')

  // Transform overall sunburst data to table rows
  const tableData = useMemo(() => {
    // Use overall_sunburst_data if available, fallback to legacy sunburst_data
    const sunburstData = data?.overall_sunburst_data || data?.sunburst_data
    if (!sunburstData || sunburstData.length === 0) return []

    const transformNode = (node: any, level: number = 0, parentId?: string): TableRow[] => {
      const rows: TableRow[] = []

      const row: TableRow = {
        id: `${parentId || 'root'}-${node.name}`,
        name: node.name,
        level,
        taskSets: node.value || 0,
        taskItems: node.task_items_count || 0,
        avgItemsPerSet: node.value > 0 ? (node.task_items_count || 0) / node.value : 0,
        color: node.itemStyle?.color || '#6B7280',
        parentId,
        children: []
      }

      rows.push(row)

      // Process children
      if (node.children && node.children.length > 0) {
        const childRows = node.children.flatMap((child: any) =>
          transformNode(child, level + 1, row.id)
        )
        row.children = childRows
        rows.push(...childRows)
      }

      return rows
    }

    return sunburstData.flatMap(node => transformNode(node))
  }, [data])

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    let filtered = tableData.filter(row => 
      row.name.toLowerCase().includes(searchTerm.toLowerCase())
    )

    // Sort data
    filtered.sort((a, b) => {
      let aValue: number | string = a[sortField]
      let bValue: number | string = b[sortField]

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = (bValue as string).toLowerCase()
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

    return filtered
  }, [tableData, searchTerm, sortField, sortDirection])

  // Get visible rows (considering expanded state)
  const visibleRows = useMemo(() => {
    const visible: TableRow[] = []
    
    const addVisibleRows = (rows: TableRow[]) => {
      for (const row of rows) {
        if (row.level === 0 || (row.parentId && expandedRows.has(row.parentId))) {
          visible.push(row)
          if (row.children && expandedRows.has(row.id)) {
            addVisibleRows(row.children)
          }
        }
      }
    }

    // Group by top level and process
    const topLevelRows = filteredAndSortedData.filter(row => row.level === 0)
    addVisibleRows(topLevelRows)

    return visible
  }, [filteredAndSortedData, expandedRows])

  const toggleExpanded = (rowId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev)
      if (newSet.has(rowId)) {
        newSet.delete(rowId)
      } else {
        newSet.add(rowId)
      }
      return newSet
    })
  }

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return null
    return sortDirection === 'asc' ? 
      <SortAsc className="w-4 h-4" /> : 
      <SortDesc className="w-4 h-4" />
  }

  // Check if we have any sunburst data (overall or legacy)
  const hasSunburstData = (data?.overall_sunburst_data && data.overall_sunburst_data.length > 0) ||
                         (data?.sunburst_data && data.sunburst_data.length > 0)

  if (!hasSunburstData) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center space-y-2">
          <Database className="h-12 w-12 text-muted-foreground mx-auto" />
          <p className="text-muted-foreground">No data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Search and Controls */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search categories..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
          />
        </div>
        <div className="text-sm text-muted-foreground">
          {visibleRows.length} items
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-auto border border-border rounded-lg bg-background">
        <table className="w-full">
          <thead className="bg-muted/50 sticky top-0 z-10">
            <tr>
              <th className="text-left p-3 font-medium text-foreground">
                <button
                  onClick={() => handleSort('name')}
                  className="flex items-center gap-2 hover:text-primary transition-colors"
                >
                  Category
                  {getSortIcon('name')}
                </button>
              </th>
              <th className="text-right p-3 font-medium text-foreground">
                <button
                  onClick={() => handleSort('taskSets')}
                  className="flex items-center gap-2 hover:text-primary transition-colors ml-auto"
                >
                  Task Sets
                  {getSortIcon('taskSets')}
                </button>
              </th>
              <th className="text-right p-3 font-medium text-foreground">
                <button
                  onClick={() => handleSort('taskItems')}
                  className="flex items-center gap-2 hover:text-primary transition-colors ml-auto"
                >
                  Task Items
                  {getSortIcon('taskItems')}
                </button>
              </th>
              <th className="text-right p-3 font-medium text-foreground">
                <button
                  onClick={() => handleSort('avgItemsPerSet')}
                  className="flex items-center gap-2 hover:text-primary transition-colors ml-auto"
                >
                  Avg/Set
                  {getSortIcon('avgItemsPerSet')}
                </button>
              </th>
            </tr>
          </thead>
          <tbody>
            <AnimatePresence>
              {visibleRows.map((row, index) => {
                const hasChildren = row.children && row.children.length > 0
                const isExpanded = expandedRows.has(row.id)
                
                return (
                  <motion.tr
                    key={row.id}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ delay: index * 0.02 }}
                    className="border-b border-border hover:bg-muted/30 transition-colors group"
                  >
                    <td className="p-3">
                      <div 
                        className="flex items-center gap-2"
                        style={{ paddingLeft: `${row.level * 20}px` }}
                      >
                        {hasChildren ? (
                          <button
                            onClick={() => toggleExpanded(row.id)}
                            className="p-1 hover:bg-muted rounded transition-colors"
                          >
                            {isExpanded ? (
                              <ChevronDown className="w-4 h-4" />
                            ) : (
                              <ChevronRight className="w-4 h-4" />
                            )}
                          </button>
                        ) : (
                          <div className="w-6" />
                        )}
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: row.color }}
                        />
                        <span className="font-medium text-foreground">
                          {row.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </div>
                    </td>
                    <td className="p-3 text-right">
                      <div className="flex items-center justify-end gap-2">
                        <span className="font-semibold text-blue-600 dark:text-blue-400">
                          {row.taskSets.toLocaleString()}
                        </span>
                        <Target className="w-4 h-4 text-blue-500 opacity-60" />
                      </div>
                    </td>
                    <td className="p-3 text-right">
                      <div className="flex items-center justify-end gap-2">
                        <span className="font-semibold text-emerald-600 dark:text-emerald-400">
                          {row.taskItems.toLocaleString()}
                        </span>
                        <BarChart3 className="w-4 h-4 text-emerald-500 opacity-60" />
                      </div>
                    </td>
                    <td className="p-3 text-right">
                      <div className="flex items-center justify-end gap-2">
                        <span className="font-semibold text-purple-600 dark:text-purple-400">
                          {row.avgItemsPerSet.toFixed(1)}
                        </span>
                        <TrendingUp className="w-4 h-4 text-purple-500 opacity-60" />
                      </div>
                    </td>
                  </motion.tr>
                )
              })}
            </AnimatePresence>
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default TaskSetsDistributionTable
