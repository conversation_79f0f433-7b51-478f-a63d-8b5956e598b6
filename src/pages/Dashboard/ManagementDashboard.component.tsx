import React from 'react'
import { motion } from 'framer-motion'
import MainLayout from '../../components/layout/MainLayout'
import LeaderboardCard from './sub-components/LeaderboardCard'
import OverviewMetricsCard from './sub-components/OverviewMetricsCard'
import UserAnalyticsCard from './sub-components/UserAnalyticsCard'
import TaskSetsAnalyticsCard from './sub-components/TaskSetsAnalyticsCard'

import TopDateFilter from './sub-components/TopDateFilter'
import useManagementDashboard from './ManagementDashboard.container'
import { useLeaderboard } from '../../hooks/useLearningStats'

/**
 * Management Dashboard Component - Modern admin dashboard with sleek animations
 * Features overview metrics, user analytics, task sets analytics, and date filtering
 */
const ManagementDashboard: React.FC = () => {
  const {
    // State
    overview,
    overviewLoading,
    overviewError,
    usersMetrics,
    usersLoading,
    usersError,
    taskSetsMetrics,
    taskSetsLoading,
    taskSetsError,
    startDate,
    endDate,
    user,
    
    // Actions
    handleDateChange,
    handleDateReset,
    refreshAll,
    refreshOverview,
    refreshUsersMetrics,
    refreshTaskSetsMetrics,
    isLoading
  } = useManagementDashboard()

  // Get leaderboard data (keeping existing functionality)
  const {
    leaderboard,
    loading: leaderboardLoading,
    error: leaderboardError
  } = useLeaderboard({ skip: 0, limit: 10 })

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94] as const
      }
    }
  }



  const staggerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  }

  return (
    <MainLayout
      title="Management Dashboard"
      description={`Admin Overview - Welcome back, ${user?.full_name || user?.username}!`}
    >
      {/* Main Container - Full height layout */}
      <div className="h-[calc(100vh-4rem)] flex min-h-0 overflow-hidden">

        {/* Left Side Content - 70% width, single scroll, no scrollbar */}
        <div className="flex-1 w-[70%] overflow-y-auto overflow-x-hidden scrollbar-hide">
          <div className="p-6 space-y-8">

            {/* Date Range Filter - Same width as other cards */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
            >
              <TopDateFilter
                startDate={startDate}
                endDate={endDate}
                onDateChange={handleDateChange}
                onReset={handleDateReset}
                onRefreshAll={refreshAll}
                isRefreshing={isLoading}
                lastUpdated={overview?.timestamp}
                cardVariants={cardVariants}
              />
            </motion.div>

            {/* Overview Metrics */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
            >
              <OverviewMetricsCard
                data={overview}
                taskSetsData={taskSetsMetrics}
                loading={overviewLoading}
                error={overviewError}
                onRefresh={refreshOverview}
                cardVariants={cardVariants}
              />
            </motion.div>

            {/* User Analytics - Race Chart */}
            <motion.div
              variants={staggerVariants}
              initial="hidden"
              animate="visible"
              className="w-full"
            >
              <motion.div
                variants={cardVariants}
                className="h-[500px] w-full"
              >
                <UserAnalyticsCard
                  data={usersMetrics}
                  loading={usersLoading}
                  error={usersError}
                  onRefresh={refreshUsersMetrics}
                  cardVariants={cardVariants}
                />
              </motion.div>
            </motion.div>

            {/* Task Sets Analytics - Interactive Table */}
            <motion.div
              variants={staggerVariants}
              initial="hidden"
              animate="visible"
              className="w-full"
            >
              <motion.div
                variants={cardVariants}
                className="h-[600px] w-full"
              >
                <TaskSetsAnalyticsCard
                  data={taskSetsMetrics}
                  loading={taskSetsLoading}
                  error={taskSetsError}
                  onRefresh={refreshTaskSetsMetrics}
                  cardVariants={cardVariants}
                />
              </motion.div>
            </motion.div>

            {/* Bottom spacing for comfortable scrolling */}
            <div className="h-16"></div>

          </div>
        </div>

        {/* Right Side - Fixed Leaderboard Only (30% width, no scrolling) */}
        <motion.div
          className="w-[30%] border-l border-border/30 bg-background/50 backdrop-blur-sm flex-shrink-0"
          variants={cardVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="h-full overflow-hidden p-6">
            <LeaderboardCard
              leaderboard={leaderboard}
              loading={leaderboardLoading}
              error={leaderboardError}
              cardVariants={cardVariants}
            />
          </div>
        </motion.div>

      </div>
    </MainLayout>
  )
}

export default ManagementDashboard
