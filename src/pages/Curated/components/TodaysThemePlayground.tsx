import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Calendar,
  Sparkles,
  Loader2,
  AlertCircle,
  MessageCircle,
  Star,
  Crown
} from 'lucide-react'
import { cn } from '../../../utils/cn'
import { CuratedService, EngagementQuestion } from '../../../services/curatedService'

interface TodaysThemeData {
  theme_name: string
  theme_name_en: string
  theme_icon: string
  category: string
  font_color: string
  theme_color: string
  background_color: string
  theme_id: string
  engagement_questions?: EngagementQuestion[]
  description?: string
  description_en?: string
}

interface TodaysThemePlaygroundProps {
  className?: string
}

const TodaysThemePlayground: React.FC<TodaysThemePlaygroundProps> = ({ className }) => {
  const [theme, setTheme] = useState<TodaysThemeData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)

  useEffect(() => {
    fetchTodaysTheme()
  }, [])

  // Auto slideshow for engagement questions
  useEffect(() => {
    if (theme?.engagement_questions && theme.engagement_questions.length > 1) {
      const interval = setInterval(() => {
        setCurrentQuestionIndex((prev) =>
          (prev + 1) % theme.engagement_questions!.length
        )
      }, 4000) // Change every 4 seconds

      return () => clearInterval(interval)
    }
  }, [theme?.engagement_questions])

  const fetchTodaysTheme = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await CuratedService.getTodaysTheme()
      setTheme(response.data)
    } catch (err) {
      console.error('Error fetching today\'s theme:', err)
      setError('Failed to load today\'s theme')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn(
          "bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 border border-border rounded-xl p-6",
          className
        )}
      >
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-8 h-8 animate-spin text-purple-500" />
          <span className="ml-3 text-purple-600 dark:text-purple-400">
            Loading today's theme...
          </span>
        </div>
      </motion.div>
    )
  }

  if (error || !theme) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn(
          "bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 border border-red-200 dark:border-red-800 rounded-xl p-6",
          className
        )}
      >
        <div className="flex items-center justify-center py-8 text-red-600 dark:text-red-400">
          <AlertCircle className="w-6 h-6" />
          <span className="ml-3">{error || 'No theme available today'}</span>
        </div>
      </motion.div>
    )
  }

  const currentQuestion = theme.engagement_questions?.[currentQuestionIndex]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "relative overflow-hidden rounded-xl border shadow-lg",
        className
      )}
      style={{
        background: `linear-gradient(135deg, ${theme.background_color}15, ${theme.theme_color}10)`,
        borderColor: `${theme.theme_color}30`
      }}
    >
      {/* Header */}
      <div className="p-6 border-b border-border/50">
        <div className="flex items-center gap-3 mb-2">
          <div className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">Today's Featured Theme</span>
          </div>
          <Crown className="w-4 h-4 text-yellow-500" />
        </div>
        
        <div className="flex items-center gap-3">
          <div 
            className="w-12 h-12 rounded-xl flex items-center justify-center text-2xl shadow-sm"
            style={{ 
              backgroundColor: theme.background_color,
              color: theme.font_color
            }}
          >
            {theme.theme_icon}
          </div>
          <div className="flex-1">
            <h3 
              className="text-xl font-bold leading-tight"
              style={{ color: theme.font_color }}
            >
              {theme.theme_name}
            </h3>
            <p className="text-sm text-muted-foreground">
              {theme.theme_name_en}
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Description */}
        {theme.description && (
          <div className="mb-6">
            <p className="text-sm text-muted-foreground mb-1">
              {theme.description}
            </p>
            {theme.description_en && (
              <p className="text-xs text-muted-foreground/70">
                {theme.description_en}
              </p>
            )}
          </div>
        )}

        {/* Engagement Questions */}
        {theme.engagement_questions && theme.engagement_questions.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <MessageCircle className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">
                Daily Question
              </span>
              {theme.engagement_questions.length > 1 && (
                <div className="flex gap-1 ml-auto">
                  {theme.engagement_questions.map((_, index) => (
                    <div
                      key={index}
                      className={cn(
                        "w-2 h-2 rounded-full transition-all duration-300",
                        index === currentQuestionIndex
                          ? "bg-current opacity-100"
                          : "bg-current opacity-30"
                      )}
                      style={{ color: theme.theme_color }}
                    />
                  ))}
                </div>
              )}
            </div>

            {currentQuestion && (
              <motion.div
                key={currentQuestionIndex}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="relative"
              >
                <div 
                  className="p-4 rounded-lg border-l-4"
                  style={{ 
                    backgroundColor: `${theme.background_color}20`,
                    borderLeftColor: theme.theme_color
                  }}
                >
                  <p 
                    className="font-medium leading-relaxed"
                    style={{ color: theme.font_color }}
                  >
                    {currentQuestion.text}
                  </p>
                  {currentQuestion.text_en && (
                    <p className="text-sm text-muted-foreground mt-2">
                      {currentQuestion.text_en}
                    </p>
                  )}
                </div>
              </motion.div>
            )}
          </div>
        )}

        {/* Theme Badge */}
        <div className="mt-6 flex items-center justify-between">
          <div 
            className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium"
            style={{ 
              backgroundColor: `${theme.theme_color}20`,
              color: theme.font_color
            }}
          >
            <Star className="w-3 h-3" />
            {theme.category}
          </div>
          
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Sparkles className="w-3 h-3" />
            <span>Featured Today</span>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default TodaysThemePlayground
