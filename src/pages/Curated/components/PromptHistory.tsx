import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { Clock, CheckCircle, AlertCircle, Loader2, RefreshCw, Eye, Edit } from 'lucide-react'
import { EditorService, GeneratedPrompt } from '../../../services/edit'
import { CuratedService } from '../../../services/curatedService'
import { QuestionsModal, SetEditModal } from '../../../components/curated'
import { cn } from '../../../utils/cn'
import { formatTimeAgo } from '../../../utils/dateTimeHelper'

export interface PromptHistoryProps {
  refreshTrigger?: number
  className?: string
}

/**
 * PromptHistory Component
 * Displays history of generated prompts with status indicators
 * Used in Playground page
 */
const PromptHistory: React.FC<PromptHistoryProps> = React.memo(({
  refreshTrigger,
  className
}) => {
  const navigate = useNavigate()
  const [prompts, setPrompts] = useState<GeneratedPrompt[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPrompt, setSelectedPrompt] = useState<GeneratedPrompt | null>(null)
  const [showQuestionsModal, setShowQuestionsModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedContentSet, setSelectedContentSet] = useState<any>(null)

  const fetchPrompts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await EditorService.getPrompts()
      setPrompts(response.data || [])
    } catch (err) {
      console.error('Error fetching prompts:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch prompts')
    } finally {
      setLoading(false)
    }
  }, [])

  // Fetch prompts on mount and when refresh trigger changes
  useEffect(() => {
    fetchPrompts()
  }, [fetchPrompts, refreshTrigger])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'pending':
        return <Loader2 className="w-4 h-4 text-yellow-500 animate-spin" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }



  const handleViewTaskItems = useCallback((prompt: GeneratedPrompt) => {
    setSelectedPrompt(prompt)
    setShowQuestionsModal(true)
  }, [])

  const handleCloseModal = useCallback(() => {
    setShowQuestionsModal(false)
    setSelectedPrompt(null)
  }, [])

  const handleEditContent = useCallback(async (prompt: GeneratedPrompt) => {
    console.log('Edit button clicked for prompt:', prompt)
    try {
      // Fetch the content set details for editing using curated_content_set_id
      if (prompt.curated_content_set_id) {
        console.log('Fetching content set for curated_content_set_id:', prompt.curated_content_set_id)
        const response = await CuratedService.getCuratedSetById(prompt.curated_content_set_id)
        console.log('Content set response:', response)
        setSelectedContentSet(response.data)
        setShowEditModal(true)
      } else {
        console.log('No curated_content_set_id found for prompt')
        alert('This content cannot be edited. No content set ID found.')
      }
    } catch (error) {
      console.error('Error fetching content set for editing:', error)
      alert('Failed to load content for editing. Please try again.')
    }
  }, [])

  const handleCloseEditModal = useCallback(() => {
    setShowEditModal(false)
    setSelectedContentSet(null)
  }, [])

  if (loading) {
    return (
      <div className={cn(
        'bg-gradient-to-br from-white/90 to-blue-50/50 dark:from-gray-900/90 dark:to-blue-950/30',
        'border-2 border-blue-200/50 dark:border-blue-800/30 rounded-xl p-3',
        'shadow-lg backdrop-blur-sm h-full flex flex-col min-h-0',
        className
      )}>
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <Clock className="w-4 h-4 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-slate-800 dark:text-white">History</h3>
              <p className="text-xs text-slate-600 dark:text-slate-400">Loading...</p>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          {Array.from({ length: 3 }, (_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-start gap-3 p-4 border border-border rounded-lg">
                <div className="w-4 h-4 bg-muted rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                </div>
                <div className="h-6 bg-muted rounded w-16" />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      'bg-gradient-to-br from-white/90 to-blue-50/50 dark:from-gray-900/90 dark:to-blue-950/30',
      'border-2 border-blue-200/50 dark:border-blue-800/30 rounded-xl p-3',
      'shadow-lg transition-all duration-300 backdrop-blur-sm h-full flex flex-col min-h-0',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3 flex-shrink-0">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            <Clock className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-slate-800 dark:text-white">History</h3>
            <p className="text-xs text-slate-600 dark:text-slate-400">Recent prompts</p>
          </div>
          {prompts.length > 0 && (
            <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
              {prompts.length}
            </span>
          )}
        </div>

        <button
          onClick={fetchPrompts}
          disabled={loading}
          className={cn(
            'p-2 rounded-lg border border-border text-muted-foreground',
            'hover:text-foreground hover:border-primary/50 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-primary/20',
            'disabled:opacity-50 disabled:cursor-not-allowed'
          )}
        >
          <RefreshCw className={cn('w-4 h-4', loading && 'animate-spin')} />
        </button>
      </div>

      {/* Error state */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg mb-4">
          <AlertCircle className="w-4 h-4 text-red-500" />
          <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
        </div>
      )}

      {/* Prompts list */}
      <div className="flex-1 overflow-hidden min-h-0">
        {prompts.length === 0 ? (
          <div className="text-center py-8 h-full flex flex-col items-center justify-center">
            <Clock className="w-12 h-12 text-slate-400 mx-auto mb-3" />
            <p className="text-slate-600 dark:text-slate-400">No generation history yet</p>
            <p className="text-sm text-slate-500 dark:text-slate-500 mt-1">
              Generated prompts will appear here
            </p>
          </div>
        ) : (
          <div className="space-y-3 h-full overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-blue-300 dark:scrollbar-thumb-blue-700 scrollbar-track-transparent">
          <AnimatePresence>
            {prompts.map((prompt, index) => (
              <motion.div
                key={prompt._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className="flex items-start gap-3 p-4 border border-border rounded-lg hover:border-primary/30 transition-colors duration-200"
              >
                {/* Status icon */}
                <div className="flex-shrink-0 mt-1">
                  {getStatusIcon(prompt.status)}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-foreground line-clamp-2 mb-2 font-medium">
                    {prompt.title || 'Untitled Prompt'}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>{formatTimeAgo(prompt.created_at)}</span>
                    {prompt.task_count && (
                      <>
                        <span>•</span>
                        <span>{prompt.task_count} tasks</span>
                      </>
                    )}
                    {prompt.story_count && (
                      <>
                        <span>•</span>
                        <span>{prompt.story_count} stories</span>
                      </>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                {prompt.status === 'completed' && (
                  <div className="flex items-center gap-2">
                    {prompt.curated_content_set_id && (
                      <button
                        onClick={() => handleViewTaskItems(prompt)}
                        className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
                      >
                        <Eye className="w-3 h-3" />
                        View
                      </button>
                    )}
                    {prompt.curated_content_set_id && (
                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          console.log('Edit button clicked!')
                          handleEditContent(prompt)
                        }}
                        className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-green-600 bg-green-50 hover:bg-green-100 rounded-lg transition-colors cursor-pointer pointer-events-auto"
                        style={{ zIndex: 10 }}
                      >
                        <Edit className="w-3 h-3" />
                        Edit
                      </button>
                    )}
                  </div>
                )}

                {/* Status badge */}
                <div className={cn(
                  'px-2 py-1 rounded-full text-xs font-medium border',
                  getStatusColor(prompt.status)
                )}>
                  {prompt.status}
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          </div>
        )}
      </div>

      {/* Task Items Modal */}
      {selectedPrompt && showQuestionsModal && selectedPrompt.curated_content_set_id && (
        <QuestionsModal
          open={showQuestionsModal}
          onOpenChange={handleCloseModal}
          contentSet={{
            _id: selectedPrompt.curated_content_set_id,
            id: selectedPrompt.curated_content_set_id,
            theme_id: '',
            title: selectedPrompt.title || 'Generated Content',
            title_en: selectedPrompt.title || 'Generated Content',
            description: 'Generated content questions',
            description_en: 'Generated content questions',
            difficulty_level: 2,
            status: 'completed',
            gentype: 'primary',
            task_item_ids: [],
            total_items: selectedPrompt.task_count || 0,
            created_at: selectedPrompt.created_at
          }}
          onTryThis={(taskSetId: string) => {
            // Navigate to task set detail page
            navigate(`/tasks/${taskSetId}`)
          }}
        />
      )}

      {/* Edit Modal */}
      {showEditModal && selectedContentSet && (
        <SetEditModal
          open={showEditModal}
          onOpenChange={handleCloseEditModal}
          contentSet={selectedContentSet}
        />
      )}
    </div>
  )
})

PromptHistory.displayName = 'PromptHistory'

export default PromptHistory
