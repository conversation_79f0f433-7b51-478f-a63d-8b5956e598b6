import React, { useState, useC<PERSON>back, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>ap, <PERSON>ader2, AlertCircle, CheckCircle, Trash2, <PERSON>lette, ChevronDown } from 'lucide-react'
import { usePromptGeneration } from '../hooks'
import { cn } from '../../../utils/cn'
import { CuratedService, Theme } from '../../../services/curatedService'

export interface PromptEditorProps {
  onGenerate?: (prompt: string, themeId?: string, contentType?: 'curated' | 'generated') => void
  className?: string
}

/**
 * PromptEditor Component
 * Admin interface for content generation with prompt input and controls
 * Used in Playground page
 */
const PromptEditor: React.FC<PromptEditorProps> = React.memo(({
  onGenerate,
  className
}) => {
  // Generate dynamic prompt based on selected theme
  const generateDynamicPrompt = (theme: Theme | null) => {
    if (!theme) {
      return `Generate educational questions for intermediate level students.

Please select a theme to get specific instructions.

`
    }

    return `Generate educational questions about ${theme.name_en} for intermediate level students.

Theme: ${theme.name_en}
Description: ${theme.description_en || theme.description || 'Explore this fascinating topic'}\n
Topics to cover:
- Key concepts and fundamentals
- Practical applications and examples
- Cultural significance and context
- Interactive learning activities\n
Make sure the questions encourage critical thinking and connect the topic to Nepali culture, history, geography, or everyday life.
`
  }

  const [prompt, setPrompt] = useState(() => generateDynamicPrompt(null))
  const [quizInstructions, setQuizInstructions] = useState('')
  const [lastGeneratedPrompt, setLastGeneratedPrompt] = useState('')
  const [showSuccess, setShowSuccess] = useState(false)

  // Theme selection state
  const [themes, setThemes] = useState<Theme[]>([])
  const [selectedTheme, setSelectedTheme] = useState<string>('')
  const [loadingThemes, setLoadingThemes] = useState(false)
  const [showThemeError, setShowThemeError] = useState(false)



  const {
    generating,
    error,
    generateContent,
    clearError
  } = usePromptGeneration()

  // Load themes on component mount
  useEffect(() => {
    loadThemes()
  }, [])

  const loadThemes = async () => {
    try {
      setLoadingThemes(true)
      const response = await CuratedService.getThemes({ is_active: true, limit: 100 })
      setThemes(response.data)

      // Update prompt if there's a selected theme
      if (selectedTheme) {
        const selectedThemeData = response.data.find(t => (t._id || t.id) === selectedTheme)
        if (selectedThemeData) {
          setPrompt(generateDynamicPrompt(selectedThemeData))
        }
      }
    } catch (error) {
      console.error('Error loading themes:', error)
    } finally {
      setLoadingThemes(false)
    }
  }



  const handleGenerate = useCallback(async () => {
    if (!prompt.trim()) return

    // Check if theme is selected
    if (!selectedTheme) {
      setShowThemeError(true);
      // Auto-hide error after 3 seconds
      setTimeout(() => setShowThemeError(false), 3000);
      return;
    }

    // Theme is required
    const success = await generateContent(prompt, selectedTheme, quizInstructions)

    if (success) {
      setLastGeneratedPrompt(prompt)
      setShowSuccess(true)
      onGenerate?.(prompt, selectedTheme, 'curated')

      // Hide success message after 3 seconds
      setTimeout(() => setShowSuccess(false), 3000)
    }
  }, [prompt, generateContent, onGenerate, selectedTheme])

  const handleClear = useCallback(() => {
    setPrompt('')
    clearError()
  }, [clearError])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      handleGenerate()
    }
  }, [handleGenerate])

  const promptLength = prompt.length
  const maxLength = 500
  const isNearLimit = promptLength > maxLength * 0.8

  return (
    <div className={cn(
      'bg-gradient-to-br from-white/90 to-green-50/50 dark:from-gray-900/90 dark:to-green-950/30',
      'border-2 border-green-200/50 dark:border-green-800/30 rounded-xl p-3 space-y-3',
      'shadow-lg transition-all duration-300 backdrop-blur-sm h-full flex flex-col',
      className
    )}>
      {/* Header */}
      <div className="flex items-center gap-3 flex-shrink-0">
        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-cyan-500 rounded-lg flex items-center justify-center">
          <Zap className="w-4 h-4 text-white" />
        </div>
        <div>
          <h2 className="text-lg font-bold text-slate-800 dark:text-white">Prompt Editor</h2>
          <p className="text-xs text-slate-600 dark:text-slate-400">
            Edit and customize prompts
          </p>
        </div>
        
        {/* Success indicator */}
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="flex items-center gap-2 text-green-600"
          >
            <CheckCircle className="w-5 h-5" />
            <span className="text-sm font-medium">Generated successfully!</span>
          </motion.div>
        )}
      </div>



      {/* Theme Selection */}
      <motion.div
        className="flex-shrink-0 space-y-3 p-4 bg-white/50 dark:bg-gray-900/50 rounded-lg border border-green-200/30 dark:border-green-800/20"
        animate={showThemeError ? {
          scale: [1, 1.02, 1],
          borderColor: ['rgb(34 197 94 / 0.3)', 'rgb(239 68 68 / 0.8)', 'rgb(34 197 94 / 0.3)']
        } : {}}
        transition={{ duration: 0.6, ease: "easeInOut" }}
      >
        <div className="flex items-center gap-2 mb-3">
          <Palette className="w-4 h-4 text-green-600 dark:text-green-400" />
          <span className="text-sm font-medium text-foreground">Select Theme</span>
        </div>

        <div className="space-y-1.5">
          <div className="relative">
            <select
              value={selectedTheme}
              onChange={(e) => {
                const themeId = e.target.value
                setSelectedTheme(themeId)
                if (themeId) {
                  setShowThemeError(false) // Clear error when theme is selected
                  // Update prompt with selected theme
                  const selectedThemeData = themes.find(t => (t._id || t.id) === themeId)
                  setPrompt(generateDynamicPrompt(selectedThemeData || null))
                } else {
                  // Reset to default prompt when no theme selected
                  setPrompt(generateDynamicPrompt(null))
                }
              }}
              disabled={loadingThemes}
              className={cn(
                'w-full px-3 py-2 rounded-lg border text-sm',
                'bg-white/80 dark:bg-gray-900/80 text-foreground',
                'focus:outline-none focus:ring-2 transition-all duration-200 appearance-none cursor-pointer',
                'disabled:opacity-50',
                showThemeError
                  ? 'border-red-200/50 dark:border-red-800/30 focus:ring-red-500/30 focus:border-red-500/50 hover:border-red-300/70 dark:hover:border-red-700/50'
                  : 'border-green-200/50 dark:border-green-800/30 focus:ring-green-500/30 focus:border-green-500/50 hover:border-green-300/70 dark:hover:border-green-700/50'
              )}
            >
              <option value="">Select a theme</option>
              {themes.map((theme) => (
                <option key={theme._id || theme.id} value={theme._id || theme.id}>
                  {theme.icon} {theme.name_en}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
          </div>
        </div>

        {/* Selected Theme Info */}
        {selectedTheme && (
          <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200/50 dark:border-green-800/30">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 rounded-lg flex items-center justify-center text-sm"
                   style={{
                     backgroundColor: `${themes.find(t => (t._id || t.id) === selectedTheme)?.color}20`,
                     color: themes.find(t => (t._id || t.id) === selectedTheme)?.color
                   }}>
                {themes.find(t => (t._id || t.id) === selectedTheme)?.icon}
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-foreground">
                  {themes.find(t => (t._id || t.id) === selectedTheme)?.name_en}
                </h4>
                <p className="text-xs text-muted-foreground">
                  {themes.find(t => (t._id || t.id) === selectedTheme)?.description_en || themes.find(t => (t._id || t.id) === selectedTheme)?.description}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Theme validation error */}
        {showThemeError && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg"
          >
            <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
            <span className="text-sm text-red-700 dark:text-red-300">Please select a theme to generate content</span>
          </motion.div>
        )}
      </motion.div>

      {/* Prompt input */}
      <div className="flex-1 flex flex-col space-y-2 min-h-0">
        <div className="flex items-center justify-between flex-shrink-0">
          <label htmlFor="prompt" className="text-sm font-medium text-foreground">
            Story Instructions
          </label>
          <div className={cn(
            'text-xs transition-colors duration-200',
            isNearLimit ? 'text-red-500' : 'text-muted-foreground'
          )}>
            {promptLength}/{maxLength}
          </div>
        </div>

        <textarea
          id="prompt"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Edit the prompt above or write your own content generation request..."
          maxLength={maxLength}
          className={cn(
            'w-full flex-1 px-3 py-2 rounded-lg border-2 border-green-200/50 dark:border-green-800/30',
            'bg-white/80 dark:bg-gray-900/80 text-foreground placeholder:text-muted-foreground',
            'focus:outline-none focus:ring-2 focus:ring-green-500/30 focus:border-green-500/50',
            'hover:border-green-300/70 dark:hover:border-green-700/50',
            'transition-all duration-300 ease-out resize-none font-mono text-sm leading-relaxed',
            'shadow-sm backdrop-blur-sm min-h-0 overflow-y-auto',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500/20'
          )}
        />
        
        {/* Helper text */}
        <p className="text-xs text-muted-foreground">
          💡 Tip: Use Ctrl+Enter (Cmd+Enter on Mac) to generate quickly
        </p>
      </div>

      {/* Quiz Instructions input */}
      <div className="space-y-2 flex-shrink-0">
        <label htmlFor="quiz-instructions" className="text-sm font-medium text-foreground">
          Quiz Instructions
        </label>
        <textarea
          id="quiz-instructions"
          value={quizInstructions}
          onChange={(e) => setQuizInstructions(e.target.value)}
          placeholder="Enter specific instructions for task generation, guidance, difficulty level, question types, etc..."
          rows={4}
          className={cn(
            'w-full px-3 py-2 rounded-lg border-2 border-blue-200/50 dark:border-blue-800/30',
            'bg-white/80 dark:bg-gray-900/80 text-foreground placeholder:text-muted-foreground',
            'focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50',
            'hover:border-blue-300/70 dark:hover:border-blue-700/50',
            'transition-all duration-300 ease-out resize-none text-sm leading-relaxed',
            'shadow-sm backdrop-blur-sm'
          )}
        />
        <p className="text-xs text-muted-foreground">
          📝 Specify instructions about question types, difficulty, format, or any special guidance for task generation
        </p>
      </div>

      {/* Error display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
        </motion.div>
      )}

      {/* Last generated prompt */}
      {lastGeneratedPrompt && lastGeneratedPrompt !== prompt && (
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground mb-1">Last generated:</p>
          <p className="text-sm text-foreground line-clamp-2">{lastGeneratedPrompt}</p>
        </div>
      )}

      {/* Action buttons */}
      <div className="flex items-center gap-3 flex-shrink-0">
        <button
          onClick={handleGenerate}
          disabled={!prompt.trim() || generating}
          className={cn(
            'flex items-center gap-2 px-6 py-3 rounded-lg font-medium',
            'bg-gradient-to-r from-green-500 to-cyan-500 text-white',
            'hover:from-green-600 hover:to-cyan-600 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-green-500/20',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'transform hover:scale-105 active:scale-95'
          )}
        >
          {generating ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Generating...</span>
            </>
          ) : (
            <>
              <Zap className="w-4 h-4" />
              <span>Generate Content</span>
            </>
          )}
        </button>

        <button
          onClick={handleClear}
          disabled={!prompt && !error}
          className={cn(
            'flex items-center gap-2 px-4 py-3 rounded-lg',
            'border border-border text-muted-foreground',
            'hover:text-foreground hover:border-primary/50 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-primary/20',
            'disabled:opacity-50 disabled:cursor-not-allowed'
          )}
        >
          <Trash2 className="w-4 h-4" />
          <span>Clear</span>
        </button>
      </div>
    </div>
  )
})

PromptEditor.displayName = 'PromptEditor'

export default PromptEditor
