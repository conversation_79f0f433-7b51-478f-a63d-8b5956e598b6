import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useLocation } from 'react-router-dom'
import {
  Mic,
  Wifi,
  WifiOff,
  Settings,
  Square,
  Loader2,
  CheckCircle,
  AlertCircle,
  Volume2,
  Headphones,
  Zap,
  Target,
  MessageCircle,
  Calendar,
  Star
} from 'lucide-react'
import MainLayout from '../../components/layout/MainLayout'
import { cn } from '../../utils/cn'
import { SocketSession, SocketConnectionState } from '../../services/socket/socketService'


interface BeginLearningComponentProps {
  connectionState: SocketConnectionState['status']
  session: SocketSession | null
  isRecording: boolean
  error: string | null
  isLoading: boolean
  difficulty: 'easy' | 'medium' | 'hard'
  numTasks: number
  taskGenerationProgress: {
    processing: boolean
    completed: boolean
    taskSetId?: string
  }
  onStartRecording: () => void
  onStopRecording: () => void
  onDisconnect: () => void
  onSettingsChange: (settings: { difficulty: 'easy' | 'medium' | 'hard', numTasks: number }) => void
  onClearError: () => void
}

/**
 * Begin Learning Component - Modern UI for audio learning sessions
 */
const BeginLearningComponent: React.FC<BeginLearningComponentProps> = ({
  connectionState,
  session,
  isRecording,
  error,
  isLoading,
  difficulty,
  numTasks,
  taskGenerationProgress,
  onStartRecording,
  onStopRecording,
  onDisconnect,
  onSettingsChange,
  onClearError
}) => {
  const location = useLocation()
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [todaysTheme, setTodaysTheme] = useState<any>(null)
  const [loadingTheme, setLoadingTheme] = useState(false)

  // Get theme data from navigation state or fetch today's theme
  const themeData = location.state?.theme || todaysTheme
  const engagementQuestions = location.state?.engagement_questions || themeData?.engagement_questions || []

  // Fetch today's theme if not provided via navigation
  useEffect(() => {
    const fetchTodaysTheme = async () => {
      if (!location.state?.theme) {
        try {
          setLoadingTheme(true)
          const { CuratedService } = await import('../../services/curatedService')
          const response = await CuratedService.getTodaysTheme()
          setTodaysTheme(response.data)
        } catch (error) {
          console.error('Error fetching today\'s theme:', error)
        } finally {
          setLoadingTheme(false)
        }
      }
    }

    fetchTodaysTheme()
  }, [location.state?.theme])

  // Auto slideshow for engagement questions
  useEffect(() => {
    if (engagementQuestions && engagementQuestions.length > 1) {
      const interval = setInterval(() => {
        setCurrentQuestionIndex((prev) =>
          (prev + 1) % engagementQuestions.length
        )
      }, 4000) // Change every 4 seconds

      return () => clearInterval(interval)
    }
  }, [engagementQuestions])
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring" as const,
        stiffness: 100,
        damping: 15
      }
    }
  }



  // Status indicators
  const getStatusColor = () => {
    switch (connectionState) {
      case 'CONNECTED': return 'text-green-500'
      case 'ACTIVE': return 'text-blue-500'
      case 'COMPLETED': return 'text-purple-500'
      case 'ERROR': return 'text-red-500'
      case 'CANCELLED': return 'text-orange-500'
      default: return 'text-gray-500'
    }
  }

  const getStatusIcon = () => {
    switch (connectionState) {
      case 'CONNECTED':
      case 'ACTIVE':
        return <Wifi className="h-5 w-5" />
      case 'COMPLETED':
        return <CheckCircle className="h-5 w-5" />
      case 'ERROR':
        return <AlertCircle className="h-5 w-5" />
      default:
        return <WifiOff className="h-5 w-5" />
    }
  }

  const isConnected = connectionState === 'CONNECTED' || connectionState === 'ACTIVE'
  const canStartRecording = !isRecording && !taskGenerationProgress.processing && !isLoading

  return (
    <MainLayout
      title="Begin Learning"
      description="Start your interactive Nepali learning session"
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-4xl mx-auto space-y-4 sm:space-y-6 lg:space-y-8"
      >
        {/* Status Header */}
        <motion.div
          variants={cardVariants}
          className="bg-card border border-border rounded-xl p-4 sm:p-6"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
            <div className="flex items-center gap-3 sm:gap-4 min-w-0">
              <div className={cn("flex items-center gap-2", getStatusColor())}>
                {getStatusIcon()}
                <span className="font-medium capitalize text-sm sm:text-base">
                  {connectionState.toLowerCase().replace('_', ' ')}
                </span>
              </div>
              {session && (
                <div className="text-xs sm:text-sm text-muted-foreground truncate">
                  Session: {session.session_id.slice(0, 8)}...
                </div>
              )}
            </div>

            <div className="flex items-center gap-2 sm:gap-3">
              <div className="text-xs sm:text-sm text-muted-foreground">
                {difficulty} • {numTasks} tasks
              </div>
              <button
                onClick={() => {/* Settings modal */}}
                className="p-1.5 sm:p-2 hover:bg-accent rounded-lg transition-colors"
              >
                <Settings className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              </button>
            </div>
          </div>
        </motion.div>

        {/* Today's Theme Engagement Questions */}
        {((themeData && engagementQuestions.length > 0) || loadingTheme) && (
          <motion.div
            variants={cardVariants}
            className="bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 dark:from-orange-900/30 dark:via-amber-900/20 dark:to-yellow-900/30 border-2 border-orange-200/60 dark:border-orange-700/50 rounded-xl p-4 sm:p-6 shadow-lg"
          >
            {loadingTheme ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-8 h-8 animate-spin text-orange-500" />
                <span className="ml-3 text-orange-600 dark:text-orange-400">
                  Loading today's theme...
                </span>
              </div>
            ) : (
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  <div
                    className="w-12 h-12 rounded-xl flex items-center justify-center font-bold text-lg shadow-lg"
                    style={{
                      backgroundColor: themeData.background_color || themeData.theme_color || '#F59E0B',
                      color: themeData.font_color || '#ffffff'
                    }}
                  >
                    {themeData.theme_icon ? (
                      <span className="text-2xl">{themeData.theme_icon}</span>
                    ) : (
                      <Star className="w-6 h-6" />
                    )}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                    <span className="text-sm font-bold text-orange-800 dark:text-orange-200 bg-orange-200/50 dark:bg-orange-800/50 px-3 py-1 rounded-full">
                      Today's Featured Theme
                    </span>
                  </div>
                  <h3 className="text-xl font-bold text-orange-900 dark:text-orange-100 mb-4">
                    {themeData.theme_name || themeData.title}
                  </h3>
                  {themeData.theme_name_en && (
                    <p className="text-sm text-orange-700 dark:text-orange-300 mb-3">
                      {themeData.theme_name_en}
                    </p>
                  )}

                  {/* Engagement Questions Slideshow */}
                  <div className="bg-gradient-to-r from-emerald-100 via-teal-50 to-cyan-100 dark:from-emerald-900/50 dark:via-teal-900/40 dark:to-cyan-900/50 rounded-xl p-5 border-2 border-emerald-200/60 dark:border-emerald-700/50 shadow-md">
                    <div className="flex items-start gap-4">
                      <div className="p-2.5 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl shadow-lg">
                        <MessageCircle className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <motion.div
                          key={currentQuestionIndex}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{ duration: 0.3 }}
                          className="mb-4"
                        >
                          <p className="text-base font-semibold text-emerald-800 dark:text-emerald-200 leading-relaxed">
                            {engagementQuestions[currentQuestionIndex].text ||
                             engagementQuestions[currentQuestionIndex].text_en}
                          </p>
                          {engagementQuestions[currentQuestionIndex].text_en &&
                           engagementQuestions[currentQuestionIndex].text && (
                            <p className="text-sm text-emerald-600 dark:text-emerald-300 mt-2 leading-relaxed">
                              {engagementQuestions[currentQuestionIndex].text_en}
                            </p>
                          )}
                        </motion.div>
                        {engagementQuestions.length > 1 && (
                          <div className="flex items-center justify-between">
                            <div className="flex gap-2">
                              {engagementQuestions.map((_: any, index: number) => (
                                <div
                                  key={index}
                                  className={cn(
                                    'w-2.5 h-2.5 rounded-full transition-all duration-300 cursor-pointer',
                                    index === currentQuestionIndex
                                      ? 'bg-gradient-to-r from-emerald-500 to-teal-600 shadow-lg scale-125'
                                      : 'bg-emerald-300 dark:bg-emerald-600 hover:bg-emerald-400 dark:hover:bg-emerald-500 hover:scale-110'
                                  )}
                                />
                              ))}
                            </div>
                            <span className="text-sm font-bold text-emerald-700 dark:text-emerald-300 bg-emerald-200/60 dark:bg-emerald-800/60 px-3 py-1.5 rounded-full shadow-sm">
                              {currentQuestionIndex + 1} of {engagementQuestions.length}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        )}



        {/* Error Display */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4"
            >
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5" />
                <div className="flex-1">
                  <h3 className="font-medium text-red-800 dark:text-red-200">Error</h3>
                  <p className="text-sm text-red-600 dark:text-red-300 mt-1">{error}</p>
                </div>
                <button
                  onClick={onClearError}
                  className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
                >
                  ×
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Control Panel */}
        <motion.div
          variants={cardVariants}
          className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border border-border rounded-xl p-4 sm:p-6 lg:p-8"
        >
          <div className="text-center space-y-4 sm:space-y-6">
            <div className="space-y-2">
              <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground">
                Interactive Learning Session
              </h2>
              <p className="text-sm sm:text-base text-muted-foreground px-2">
                Connect and start speaking to generate personalized learning tasks
              </p>
            </div>

            {/* Recording Controls */}
            <div className="flex flex-col items-center gap-4 sm:gap-6">
              <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4">
                {!isRecording ? (
                  <motion.div className="relative">
                    {/* Subtle background glow */}
                    <motion.div
                      animate={{
                        scale: canStartRecording ? [1, 1.05, 1] : 1,
                        opacity: canStartRecording ? [0.1, 0.2, 0.1] : 0.1,
                      }}
                      transition={{
                        duration: 3,
                        repeat: canStartRecording ? Infinity : 0,
                        ease: "easeInOut"
                      }}
                      className="absolute inset-0 bg-gradient-to-r from-emerald-400 to-green-500 rounded-xl blur-lg"
                    />

                    <motion.button
                      whileHover={{
                        scale: 1.05,
                        boxShadow: "0 20px 40px rgba(34, 197, 94, 0.4)"
                      }}
                      whileTap={{ scale: 0.95 }}
                      onClick={onStartRecording}
                      disabled={!canStartRecording}
                      className={cn(
                        "relative flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold transition-all duration-300",
                        "bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white text-sm sm:text-base",
                        "disabled:opacity-50 disabled:cursor-not-allowed",
                        "shadow-xl hover:shadow-2xl w-full sm:w-auto border-2 border-emerald-400/50",
                        "transform-gpu"
                      )}
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        >
                          <Loader2 className="h-5 w-5 sm:h-6 sm:w-6" />
                        </motion.div>
                      ) : (
                        <motion.div
                          animate={{
                            scale: canStartRecording ? [1, 1.15, 1] : 1,
                            rotate: canStartRecording ? [0, 8, -8, 0] : 0,
                          }}
                          transition={{
                            duration: 2,
                            repeat: canStartRecording ? Infinity : 0,
                            ease: "easeInOut"
                          }}
                        >
                          <Mic className="h-5 w-5 sm:h-6 sm:w-6" />
                        </motion.div>
                      )}
                      <span>
                        {isLoading ? 'Starting...' : 'Start Recording'}
                      </span>
                    </motion.button>
                  </motion.div>
                ) : (
                  <motion.div className="relative">
                    {/* Pulsing red background for recording */}
                    <motion.div
                      animate={{
                        scale: [1, 1.15, 1],
                        opacity: [0.4, 0.7, 0.4],
                      }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="absolute inset-0 bg-gradient-to-r from-red-400 to-rose-500 rounded-xl blur-lg"
                    />
                    <motion.div
                      animate={{
                        scale: [1, 1.08, 1],
                        opacity: [0.5, 0.8, 0.5],
                      }}
                      transition={{
                        duration: 0.8,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 0.2
                      }}
                      className="absolute inset-0 bg-gradient-to-r from-rose-400 to-red-500 rounded-xl blur-md"
                    />

                    <motion.button
                      whileHover={{
                        scale: 1.05,
                        boxShadow: "0 20px 40px rgba(239, 68, 68, 0.4)"
                      }}
                      whileTap={{ scale: 0.95 }}
                      animate={{
                        y: [0, -1, 0],
                      }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      onClick={onStopRecording}
                      className={cn(
                        "relative flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold transition-all duration-300",
                        "bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white text-sm sm:text-base",
                        "shadow-xl hover:shadow-2xl w-full sm:w-auto border-2 border-red-400/50",
                        "transform-gpu"
                      )}
                    >
                      <motion.div
                        animate={{
                          scale: [1, 0.9, 1],
                        }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        <Square className="h-5 w-5 sm:h-6 sm:w-6" />
                      </motion.div>
                      <motion.span
                        animate={{
                          opacity: [1, 0.8, 1],
                        }}
                        transition={{
                          duration: 1.2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        Stop Recording
                      </motion.span>
                    </motion.button>
                  </motion.div>
                )}

                {(isConnected || isRecording) && (
                  <button
                    onClick={onDisconnect}
                    className="px-3 sm:px-4 py-2 text-xs sm:text-sm border border-border rounded-lg hover:bg-accent transition-colors w-full sm:w-auto"
                  >
                    Disconnect
                  </button>
                )}
              </div>
            </div>

            {/* Recording Indicator */}
            <AnimatePresence>
              {isRecording && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex items-center gap-3 text-red-600"
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="w-3 h-3 bg-red-600 rounded-full"
                  />
                  <span className="font-medium">Recording in progress...</span>
                  <Volume2 className="h-4 w-4" />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>

        {/* Task Generation Progress */}
        <AnimatePresence>
          {taskGenerationProgress.processing && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              variants={cardVariants}
              className="bg-card border border-border rounded-xl p-6 space-y-4"
            >
              <div className="flex items-center gap-4">
                <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                <div>
                  <h3 className="font-medium text-foreground">Generating Learning Tasks</h3>
                  <p className="text-sm text-muted-foreground">
                    Processing your audio to create personalized tasks...
                  </p>
                </div>
              </div>
              <div className="pt-2">
                <p className="text-xs text-muted-foreground">
                  Please keep this page open while we process your audio.
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {taskGenerationProgress.completed && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              variants={cardVariants}
              className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6"
            >
              <div className="flex items-center gap-4">
                <CheckCircle className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="font-medium text-green-800 dark:text-green-200">
                    Tasks Generated Successfully!
                  </h3>
                  <p className="text-sm text-green-600 dark:text-green-300">
                    Redirecting to your new learning tasks...
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"
        >
          <motion.div variants={cardVariants} className="bg-card border border-border rounded-xl p-4 sm:p-6">
            <div className="flex items-center gap-2 sm:gap-3 mb-3">
              <div className="p-1.5 sm:p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Headphones className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-medium text-sm sm:text-base text-card-foreground">Audio Learning</h3>
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Speak naturally and get instant feedback on pronunciation and comprehension.
            </p>
          </motion.div>

          <motion.div variants={cardVariants} className="bg-card border border-border rounded-xl p-4 sm:p-6">
            <div className="flex items-center gap-2 sm:gap-3 mb-3">
              <div className="p-1.5 sm:p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <Zap className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-medium text-sm sm:text-base text-card-foreground">Real-time Processing</h3>
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Advanced AI processes your speech in real-time to create personalized tasks.
            </p>
          </motion.div>

          <motion.div variants={cardVariants} className="bg-card border border-border rounded-xl p-4 sm:p-6 sm:col-span-2 lg:col-span-1">
            <div className="flex items-center gap-2 sm:gap-3 mb-3">
              <div className="p-1.5 sm:p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <Target className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-medium text-sm sm:text-base text-card-foreground">Adaptive Difficulty</h3>
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Tasks automatically adjust to your skill level for optimal learning progress.
            </p>
          </motion.div>
        </motion.div>
      </motion.div>
    </MainLayout>
  )
}

export default BeginLearningComponent
