import React from 'react'
import { motion } from 'framer-motion'
import { LoginComponentProps } from '../types'
import EmailOrUsernameInput from '../shared/EmailOrUsernameInput'
import PasswordInput from '../shared/PasswordInput'
import SubmitButton from '../shared/SubmitButton'

/**
 * Login Component - Pure UI component for login form
 */
const LoginComponent: React.FC<LoginComponentProps> = ({
  formData,
  errors,
  isLoading,
  onInputChange,
  onSubmit,
  onClearError,
}) => {
  // Form validation for submit button
  const isFormValid = () => {
    return formData.emailOrUsername?.trim() && formData.password?.trim()
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <motion.div
        className="text-center mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <motion.h1
          className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
        >
          Welcome Back
        </motion.h1>
        <motion.p
          className="text-gray-600 dark:text-gray-400"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          Sign in to your account to continue learning
        </motion.p>
      </motion.div>

      {/* General Error Display */}
      {errors.general && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Authentication Failed
                </h3>
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.general}
                </p>
                {errors.general.includes('Admin role required') && (
                  <p className="mt-2 text-xs text-red-500 dark:text-red-400">
                    This application requires administrator privileges. Please contact your system administrator if you believe you should have access.
                  </p>
                )}
              </div>
            </div>
            <button
              onClick={onClearError}
              className="flex-shrink-0 text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </motion.div>
      )}

      <motion.form
        onSubmit={onSubmit}
        className="space-y-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        {/* Email or Username field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <EmailOrUsernameInput
            value={formData.emailOrUsername || ''}
            error={errors.emailOrUsername}
            onChange={(value) => onInputChange('emailOrUsername', value)}
            disabled={isLoading}
          />
        </motion.div>

        {/* Password field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
        >
          <PasswordInput
            value={formData.password || ''}
            error={errors.password}
            onChange={(value) => onInputChange('password', value)}
            disabled={isLoading}
            placeholder="Enter your password"
          />
        </motion.div>

        {/* Submit button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <SubmitButton
            mode="login"
            isLoading={isLoading}
            disabled={isLoading || !isFormValid()}
          />
        </motion.div>
      </motion.form>
    </motion.div>
  )
}

export default LoginComponent
