import React from 'react'
import { motion } from 'framer-motion'
import {
  Loader2,
  Headphones,
  CheckCircle,
  Clock,
  Filter,
  ChevronRight,
  Music,
  AlertCircle,
  FileText,
  X,
  Eye
} from 'lucide-react'
import MainLayout from '../../components/layout/MainLayout'
import { cn } from '../../utils/cn'
import { formatLocalDate, formatTimeAgo } from '../../utils/dateTimeHelper'
import { TaskSet, TaskSetFilter } from './Tasks.container'
import type { TaskSetFilterValues } from '../../services/task/taskService'

interface TasksComponentProps {
  loading: boolean
  taskSets: TaskSet[]
  error: string | null
  filter: TaskSetFilter
  filterValues: TaskSetFilterValues | null
  loadingFilters: boolean
  totalItems: number
  totalPages: number
  onFilterChange: (filter: TaskSetFilter) => void
  onPageChange: (page: number) => void
  onPageSizeChange: (limit: number) => void
  onTaskSetClick: (taskSetId: string) => void
  onViewOriginal: (originalTaskSetId: string) => void
  onRefresh: () => void
}

/**
 * Tasks Component - Pure UI component for task sets list
 */
const TasksComponent: React.FC<TasksComponentProps> = ({
  loading,
  taskSets,
  error,
  filter,
  filterValues,
  loadingFilters,
  totalItems,
  totalPages,
  onFilterChange,
  onPageChange,
  onPageSizeChange,
  onTaskSetClick,
  onViewOriginal,
  onRefresh
}) => {
  // Helper function to get display name from input_content
  const getTaskSetDisplayName = (taskSet: any) => {
    if (!taskSet?.input_content) return `Task Set ${taskSet._id || taskSet.id}`

    // If input_content is a string, return it
    if (typeof taskSet.input_content === 'string') {
      return taskSet.input_content
    }

    // If input_content is an object, extract meaningful name
    if (typeof taskSet.input_content === 'object') {
      return taskSet.input_content.file_name ||
             taskSet.input_content.object_name ||
             'Audio Task Set'
    }

    return `Task Set ${taskSet._id || taskSet.id}`
  }

  // Page-level animation variants
  const pageVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.98
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.4, 0, 0.2, 1] as const,
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  }

  // Enhanced Animation variants for task grid
  const containerVariants = {
    hidden: {
      opacity: 0,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.4, 0, 0.2, 1] as const,
        staggerChildren: 0.06,
        delayChildren: 0.2
      }
    }
  }

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 40,
      scale: 0.85,
      rotateX: 20,
      filter: "blur(4px)"
    },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      filter: "blur(0px)",
      transition: {
        type: "spring" as const,
        stiffness: 100,
        damping: 15,
        delay: index * 0.1 // Staggered delay based on index
      }
    }),
    hover: {
      scale: 1.02,
      y: -4,
      rotateX: 2,
      boxShadow: "0 20px 40px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(59, 130, 246, 0.1)",
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 20,
        duration: 0.2
      }
    }
  }

  // Progress bar animation variant
  const progressVariants = {
    hidden: { width: 0 },
    visible: (progress: number) => ({
      width: `${progress}%`,
      transition: {
        duration: 1.2,
        ease: [0.4, 0, 0.2, 1] as const,
        delay: 0.5
      }
    })
  }



  // Calculate progress
  const calculateProgress = (taskSet: TaskSet) => {
    // First try using attempted_tasks vs total_tasks if available
    if (taskSet.attempted_tasks !== undefined && taskSet.total_tasks !== undefined && taskSet.total_tasks > 0) {
      return Math.round((taskSet.attempted_tasks / taskSet.total_tasks) * 100)
    }

    // Fallback to using tasks array length
    const totalTasks = taskSet.tasks?.length || taskSet.total_tasks || 0
    if (totalTasks > 0) {
      // Use attempted_tasks if available, otherwise use scored
      const completedTasks = taskSet.attempted_tasks !== undefined ? taskSet.attempted_tasks : (taskSet.scored || 0)
      return Math.round((completedTasks / totalTasks) * 100)
    }

    return 0
  }

  // Filter Section Component
  const filterSection = (
    <div className="bg-gradient-to-br from-white/90 to-blue-50/50 dark:from-gray-900/90 dark:to-blue-950/30 border-2 border-blue-200/50 dark:border-blue-800/30 rounded-2xl overflow-hidden shadow-lg backdrop-blur-sm">
      <div className="flex items-center gap-3 p-6 border-b border-blue-200/30 dark:border-blue-800/30 bg-white/50 dark:bg-gray-900/50">
        <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg">
          <Filter className="w-4 h-4" />
        </div>
        <span className="font-semibold text-slate-800 dark:text-white">Task Filters</span>
      </div>

      <div className="p-3 sm:p-4 lg:p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">

          {/* Status Filter */}
          <div className="space-y-1.5 sm:space-y-2">
            <label className="text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300">Status</label>
            <div className="relative">
              <select
                value={filter.status || 'all'}
                onChange={(e) => onFilterChange({
                  ...filter,
                  status: e.target.value === 'all' ? undefined : e.target.value,
                  page: 1
                })}
                className={cn(
                  'w-full px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg sm:rounded-xl border-2 border-blue-200/50 dark:border-blue-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50',
                  'hover:border-blue-300/70 dark:hover:border-blue-700/50',
                  'transition-all duration-300 ease-out appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
                disabled={loadingFilters}
              >
                <option value="all">✨ All Status</option>
                {loadingFilters ? (
                  <option disabled>Loading...</option>
                ) : (
                  filterValues?.status?.map((status) => (
                    <option key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                    </option>
                  ))
                )}
              </select>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>

          {/* Source Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">Source</label>
            <div className="relative">
              <select
                value={(filter as any).source || 'all'}
                onChange={(e) => onFilterChange({
                  ...filter,
                  source: e.target.value === 'all' ? undefined : e.target.value,
                  page: 1
                } as any)}
                className={cn(
                  'w-full px-4 py-3 rounded-xl border-2 border-blue-200/50 dark:border-blue-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50',
                  'hover:border-blue-300/70 dark:hover:border-blue-700/50',
                  'transition-all duration-300 ease-out appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
                disabled={loadingFilters}
              >
                <option value="all">🌟 All Sources</option>
                {loadingFilters ? (
                  <option disabled>Loading...</option>
                ) : (
                  filterValues?.source?.map((source) => (
                    <option key={source} value={source}>
                      {source.charAt(0).toUpperCase() + source.slice(1).replace('_', ' ')}
                    </option>
                  ))
                )}
              </select>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>

          {/* Input Type Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">Type</label>
            <div className="relative">
              <select
                value={filter.input_type || 'all'}
                onChange={(e) => onFilterChange({
                  ...filter,
                  input_type: e.target.value === 'all' ? undefined : e.target.value,
                  page: 1
                })}
                className={cn(
                  'w-full px-4 py-3 rounded-xl border-2 border-blue-200/50 dark:border-blue-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50',
                  'hover:border-blue-300/70 dark:hover:border-blue-700/50',
                  'transition-all duration-300 ease-out appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
              >
                <option value="all">🎯 All Types</option>
                <option value="audio">🎵 Audio</option>
                <option value="text">📝 Text</option>
                <option value="image">🖼️ Image</option>
              </select>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>

          {/* Sort Options */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">Sort by</label>
            <div className="relative">
              <select
                value={`${filter.sort_by}_${filter.sort_order}`}
                onChange={(e) => {
                  const [sort_by, sort_order] = e.target.value.split('_')
                  const newFilter = {
                    ...filter,
                    sort_by,
                    sort_order: parseInt(sort_order),
                    page: 1
                  }
                  console.log('Sort filter changed:', { sort_by, sort_order: parseInt(sort_order) })
                  onFilterChange(newFilter)
                }}
                className={cn(
                  'w-full px-4 py-3 rounded-xl border-2 border-blue-200/50 dark:border-blue-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50',
                  'hover:border-blue-300/70 dark:hover:border-blue-700/50',
                  'transition-all duration-300 ease-out appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
                disabled={loadingFilters}
              >
                {loadingFilters ? (
                  <option disabled>Loading...</option>
                ) : (
                  filterValues?.sort_by?.flatMap((sortBy) =>
                    filterValues?.sort_type?.map((sortType) => {
                      const sortOrder = sortType === 'desc' ? -1 : 1
                      const displayName = sortBy.charAt(0).toUpperCase() + sortBy.slice(1).replace('_', ' ')
                      const orderText = sortType === 'desc' ? '↓' : '↑'
                      const label = `${displayName} ${orderText}`
                      return (
                        <option key={`${sortBy}_${sortOrder}`} value={`${sortBy}_${sortOrder}`}>
                          {label}
                        </option>
                      )
                    })
                  )
                )}
              </select>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Clear Filters */}
        {(filter.status || filter.input_type || (filter as any).source || filter.sort_by !== 'created_at' || filter.sort_order !== -1) && (
          <div className="mt-4 flex justify-center">
            <button
              onClick={() => onFilterChange({
                page: 1,
                limit: filter.limit,
                sort_by: 'created_at',
                sort_order: -1
              })}
              className="flex items-center gap-2 px-4 py-2 text-sm text-slate-600 dark:text-slate-400 hover:text-red-600 dark:hover:text-red-400 transition-colors rounded-xl hover:bg-red-50 dark:hover:bg-red-950/20 border-2 border-red-200 dark:border-red-800"
            >
              <X className="h-3 w-3" />
              Clear All Filters
            </button>
          </div>
        )}
      </div>
    </div>
  )

  // Pagination Section Component
  const paginationSection = !loading && taskSets.length > 0 && (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
      {/* Page Size Selector */}
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">Show:</span>
        <select
          value={filter.limit}
          onChange={(e) => onPageSizeChange(parseInt(e.target.value))}
          className="px-3 py-1.5 text-sm border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
        >
          <option value={6}>6 per page</option>
          <option value={12}>12 per page</option>
          <option value={24}>24 per page</option>
          <option value={48}>48 per page</option>
        </select>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center gap-2">
          <button
            onClick={() => onPageChange(filter.page - 1)}
            disabled={filter.page <= 1}
            className="px-3 py-2 text-sm border border-border rounded-lg hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          <span className="px-4 py-2 text-sm text-muted-foreground">
            Page {filter.page} of {totalPages}
          </span>

          <button
            onClick={() => onPageChange(filter.page + 1)}
            disabled={filter.page >= totalPages}
            className="px-3 py-2 text-sm border border-border rounded-lg hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}

      {/* Results Info */}
      <div className="text-sm text-muted-foreground">
        Showing {((filter.page - 1) * filter.limit) + 1} to {Math.min(filter.page * filter.limit, totalItems)} of {totalItems} results
      </div>
    </div>
  )

  return (
    <MainLayout
      title="Playground History"
      description="View and manage your completed learning tasks"
      topContent={filterSection}
      bottomContent={paginationSection}
    >
      <div className="p-4 lg:p-6">
        <motion.div
          variants={pageVariants}
          initial="hidden"
          animate="visible"
          className="space-y-4"
        >
        {/* Error state */}
        {error && !loading && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5" />
            <div>
              <h3 className="font-medium text-red-800 dark:text-red-200">Error Loading Tasks</h3>
              <p className="text-sm text-red-600 dark:text-red-300 mt-1">{error}</p>
              <button
                onClick={onRefresh}
                className="mt-2 text-sm text-red-600 dark:text-red-400 hover:underline"
              >
                Try again
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Enhanced Loading state */}
      {loading ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex flex-col items-center justify-center py-12"
        >
          <motion.div
            className="relative"
            animate={{
              scale: [1, 1.1, 1],
              rotate: [0, 180, 360]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <motion.div
              animate={{
                scale: [0.8, 1.2, 0.8],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Music className="h-4 w-4 absolute inset-0 m-auto text-blue-400" />
            </motion.div>
          </motion.div>
          <motion.h3
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-lg font-medium mt-4 text-foreground"
          >
            Loading Task Sets
          </motion.h3>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="text-sm text-muted-foreground"
          >
            Please wait while we fetch your learning history...
          </motion.p>
        </motion.div>
      ) : taskSets.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center py-12 px-6 bg-card border border-border rounded-xl"
        >
          <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-muted mb-4">
            <Filter className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-xl font-medium mb-2 text-card-foreground">No task sets found</h3>
          <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
            Start your learning journey by creating new task sets
          </p>
          <button
            onClick={() => window.location.href = '/begin-learning'}
            className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            Start Learning
            <ChevronRight className="h-4 w-4" />
          </button>
        </motion.div>
      ) : (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3 sm:gap-4 lg:gap-6"
        >
          {taskSets.map((taskSet, index) => {
            const progress = calculateProgress(taskSet)
            const isCompleted = taskSet.status === 'completed'

            return (
              <motion.div
                key={taskSet._id || taskSet.id || index}
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                custom={index}
                whileHover="hover"
                className="cursor-pointer"
                onClick={() => onTaskSetClick(taskSet._id || taskSet.id || '')}
              >
                <div className="bg-card border border-border rounded-xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300 hover:scale-[1.01] hover:border-primary/20">
                  {/* Thumbnail Image */}
                  {taskSet.thumbnail_metadata?.url ? (
                    <div className="relative h-32 sm:h-36 lg:h-40 overflow-hidden bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-900">
                      <img
                        src={taskSet.thumbnail_metadata.url}
                        alt={taskSet.title || taskSet.thumbnail || 'Task thumbnail'}
                        className="w-full h-full object-contain"
                        onError={(e) => {
                          // Hide image on error and show fallback
                          e.currentTarget.style.display = 'none'
                          const fallback = e.currentTarget.nextElementSibling as HTMLElement
                          if (fallback) fallback.style.display = 'flex'
                        }}
                      />
                      {/* Fallback gradient background */}
                      <div
                        className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"
                        style={{ display: 'none' }}
                      >
                        <div className="text-white text-center">
                          {taskSet.input_type === 'audio' ? (
                            <Headphones className="h-8 w-8 mx-auto mb-2" />
                          ) : (
                            <FileText className="h-8 w-8 mx-auto mb-2" />
                          )}
                          <p className="text-sm font-medium">
                            {taskSet.title || 'Task Set'}
                          </p>
                        </div>
                      </div>
                      {/* Overlay with status */}
                      <div className="absolute top-2 right-2 flex items-center gap-2">
                        <div className={cn(
                          "p-1 rounded-full backdrop-blur-sm",
                          isCompleted
                            ? "bg-green-500/80"
                            : "bg-amber-500/80"
                        )}>
                          {isCompleted ? (
                            <CheckCircle className="h-3 w-3 text-white" />
                          ) : (
                            <Clock className="h-3 w-3 text-white" />
                          )}
                        </div>
                      </div>
                      {/* Subtle hover overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  ) : (
                    /* Fallback when no thumbnail */
                    <div className="relative h-32 sm:h-36 lg:h-40 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <div className="text-white text-center">
                        {taskSet.input_type === 'audio' ? (
                          <Headphones className="h-8 w-8 mx-auto mb-2" />
                        ) : (
                          <FileText className="h-8 w-8 mx-auto mb-2" />
                        )}
                        <p className="text-sm font-medium">
                          {taskSet.title || 'Task Set'}
                        </p>
                      </div>
                      {/* Status overlay */}
                      <div className="absolute top-2 right-2">
                        <div className={cn(
                          "p-1 rounded-full backdrop-blur-sm",
                          isCompleted
                            ? "bg-green-500/80"
                            : "bg-amber-500/80"
                        )}>
                          {isCompleted ? (
                            <CheckCircle className="h-3 w-3 text-white" />
                          ) : (
                            <Clock className="h-3 w-3 text-white" />
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Card Content */}
                  <div className="p-3 sm:p-4 lg:p-5">
                    {/* Header with type indicator */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className={cn(
                          "px-2 py-1 rounded-full text-xs font-medium",
                          taskSet.input_type === 'audio'
                            ? "bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                            : "bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300"
                        )}>
                          {taskSet.input_type === 'audio' ? 'Audio' : 'Text'}
                        </div>
                        {taskSet.gentype && (
                          <div className={cn(
                            "px-2 py-1 rounded-full text-xs font-medium",
                            taskSet.gentype === 'follow_up'
                              ? "bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300"
                              : taskSet.gentype === 'curated'
                              ? "bg-emerald-100 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300"
                              : "bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-300"
                          )}>
                            {taskSet.gentype === 'follow_up' ? 'Follow-up' :
                             taskSet.gentype === 'curated' ? 'Curated' :
                             taskSet.gentype === 'primary' ? 'Primary' : taskSet.gentype}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {/* View Original button */}
                        {taskSet.original_task_set_id && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              onViewOriginal(taskSet.original_task_set_id!)
                            }}
                            className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold text-white bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105"
                            title="View Original Task Set Questions"
                          >
                            <Eye className="h-3.5 w-3.5" />
                            View Original
                          </button>
                        )}
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>

                  {/* Content */}
                  <div className="mb-2">
                    {/* Display task set title if available */}
                    {taskSet.title && (
                      <h3 className="font-semibold text-card-foreground line-clamp-2 text-xs sm:text-sm lg:text-base">
                        {taskSet.title}
                      </h3>
                    )}
                    {/* Fallback to display name if no title */}
                    {!taskSet.title && (
                      <h3 className="font-semibold text-card-foreground line-clamp-2 text-xs sm:text-sm lg:text-base">
                        {getTaskSetDisplayName(taskSet)}
                      </h3>
                    )}
                    {/* Display additional info from input_content if available */}
                    {typeof taskSet.input_content === 'object' && taskSet.input_content !== null && (
                      <>
                        {taskSet.input_content.file_name && (
                          <p className="text-xs text-muted-foreground mt-1 line-clamp-1">
                            {taskSet.input_content.file_name}
                          </p>
                        )}
                        {!taskSet.input_content.file_name && taskSet.input_content.object_name && (
                          <p className="text-xs text-muted-foreground mt-1 line-clamp-1">
                            {taskSet.input_content.object_name}
                          </p>
                        )}
                      </>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                    <span className="truncate">{formatLocalDate(taskSet.created_at)}</span>
                    <span className="truncate ml-2" title={`Raw timestamp: ${taskSet.created_at}`}>
                      {formatTimeAgo(taskSet.created_at)}
                    </span>
                  </div>

                  {/* Progress */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-card-foreground">Progress</span>
                      <span className={cn(
                        "text-xs font-medium",
                        progress === 100 ? "text-green-600 dark:text-green-400" : "text-blue-600 dark:text-blue-400"
                      )}>
                        {progress}%
                      </span>
                    </div>

                    <div className="w-full bg-muted rounded-full h-1.5 overflow-hidden">
                      <motion.div
                        variants={progressVariants}
                        initial="hidden"
                        animate="visible"
                        custom={progress}
                        className={cn(
                          "h-1.5 rounded-full relative",
                          progress === 100
                            ? "bg-gradient-to-r from-green-400 to-green-500"
                            : "bg-gradient-to-r from-blue-400 to-blue-500"
                        )}
                      >
                        {/* Flowing white highlight effect */}
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                          animate={{
                            x: ['-100%', '100%']
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 1
                          }}
                        />
                      </motion.div>
                    </div>

                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>
                        {taskSet.attempted_tasks !== undefined ? taskSet.attempted_tasks : (taskSet.scored || 0)} of {taskSet.total_tasks || taskSet.tasks?.length || 0} tasks
                      </span>
                      {taskSet.total_score !== undefined && taskSet.scored !== undefined && (
                        <span>Score: {taskSet.scored}/{taskSet.total_score}</span>
                      )}
                    </div>
                  </div>
                  </div> {/* Close card content div */}
                </div> {/* Close main card div */}
              </motion.div>
            )
          })}
        </motion.div>
      )}
      </motion.div>
      </div>
    </MainLayout>
  )
}

export default TasksComponent
