import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Shield, AlertCircle, CheckCircle } from 'lucide-react'
import userManagementService, { User, ChangeRoleRequest } from '../../../services/userManagement/userManagementService'
import { cn } from '../../../utils/cn'

interface ChangeRoleModalProps {
  isOpen: boolean
  onClose: () => void
  user: User | null
  onSuccess?: () => void
}

const ROLES = [
  { value: 'agent', label: 'Agent', description: 'Basic permissions' },
  { value: 'supervisor', label: 'Supervisor', description: 'Elevated permissions' },
  { value: 'admin', label: 'Admin', description: 'Full system access' }
]

/**
 * Change Role Modal - Change user role
 */
const ChangeRoleModal: React.FC<ChangeRoleModalProps> = ({
  isOpen,
  onClose,
  user,
  onSuccess
}) => {
  const [selectedRole, setSelectedRole] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Reset form when modal opens/closes or user changes
  useEffect(() => {
    if (isOpen && user) {
      setSelectedRole(user.role)
      setError(null)
      setSuccess(null)
    } else {
      setSelectedRole('')
      setError(null)
      setSuccess(null)
    }
  }, [isOpen, user])

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !selectedRole) return

    // Check if role actually changed
    if (selectedRole === user.role) {
      setError('Please select a different role')
      return
    }

    setLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await userManagementService.changeUserRole(
        user.id,
        { new_role: selectedRole } as ChangeRoleRequest
      )

      // API returns message field on success
      if (response.message) {
        setSuccess(response.message)
        onSuccess?.()

        // Auto-close after success
        setTimeout(() => {
          onClose()
        }, 2000)
      } else {
        setError('Failed to change role')
      }
    } catch (err: any) {
      setError(err.message || 'Failed to change role')
    } finally {
      setLoading(false)
    }
  }

  // Get role badge styling
  const getRoleBadge = (role: string) => {
    const styles = {
      admin: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
      supervisor: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      agent: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    }
    
    return styles[role as keyof typeof styles] || 'bg-gray-100 text-gray-800'
  }

  if (!isOpen || !user) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="bg-background border border-border rounded-2xl shadow-2xl w-full max-w-md overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-border">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-foreground">Change Role</h2>
                <p className="text-sm text-muted-foreground">Update user permissions</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* User Info */}
            <div className="mb-6 p-4 bg-muted/30 rounded-xl">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-foreground">{user.full_name || user.username}</p>
                  <p className="text-sm text-muted-foreground">@{user.username}</p>
                </div>
                <span className={cn(
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  getRoleBadge(user.role)
                )}>
                  <Shield className="w-3 h-3 mr-1" />
                  {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                </span>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center gap-2"
              >
                <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                <span className="text-sm text-red-700 dark:text-red-400">{error}</span>
              </motion.div>
            )}

            {/* Success Message */}
            {success && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                <span className="text-sm text-green-700 dark:text-green-400">{success}</span>
              </motion.div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Role Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-3">
                  Select New Role
                </label>
                <div className="space-y-2">
                  {ROLES.map((role) => (
                    <label
                      key={role.value}
                      className={cn(
                        'flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-all',
                        selectedRole === role.value
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      )}
                    >
                      <input
                        type="radio"
                        name="role"
                        value={role.value}
                        checked={selectedRole === role.value}
                        onChange={(e) => setSelectedRole(e.target.value)}
                        className="sr-only"
                      />
                      <div className={cn(
                        'w-4 h-4 rounded-full border-2 flex items-center justify-center',
                        selectedRole === role.value
                          ? 'border-primary bg-primary'
                          : 'border-border'
                      )}>
                        {selectedRole === role.value && (
                          <div className="w-2 h-2 rounded-full bg-white" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-foreground">{role.label}</span>
                          <span className={cn(
                            'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                            getRoleBadge(role.value)
                          )}>
                            <Shield className="w-3 h-3 mr-1" />
                            {role.value}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground">{role.description}</p>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  disabled={loading}
                  className="flex-1 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading || selectedRole === user.role || !selectedRole}
                  className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Changing...' : 'Change Role'}
                </button>
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default ChangeRoleModal
