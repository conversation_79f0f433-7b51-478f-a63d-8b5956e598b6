import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MoreHorizontal, 
  Shield, 
  Key, 
  Trash2, 
  UserX,
  ChevronDown
} from 'lucide-react'
import { User } from '../../../services/userManagement/userManagementService'
import { cn } from '../../../utils/cn'

interface UserActionsDropdownProps {
  user: User
  currentUserRole: string
  onChangeRole: (user: User) => void
  onResetPassword: (user: User) => void
  onDeleteUser: (user: User) => void
}

/**
 * User Actions Dropdown - Provides admin actions for user management
 */
const UserActionsDropdown: React.FC<UserActionsDropdownProps> = ({
  user,
  currentUserRole,
  onChangeRole,
  onResetPassword,
  onDeleteUser
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Check if current user has admin permissions
  const isAdmin = currentUserRole === 'admin'
  
  // Don't show actions for non-admin users
  if (!isAdmin) {
    return null
  }

  const handleAction = (action: () => void) => {
    action()
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 hover:bg-muted/50 rounded-lg transition-colors group"
        title="User actions"
      >
        <MoreHorizontal className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
      </button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.15 }}
            className="absolute right-0 top-full mt-2 w-48 bg-background border border-border rounded-xl shadow-lg z-50 overflow-hidden"
          >
            <div className="py-2">
              {/* Change Role */}
              <button
                onClick={() => handleAction(() => onChangeRole(user))}
                className="w-full px-4 py-2 text-left text-sm hover:bg-muted/50 transition-colors flex items-center gap-3"
              >
                <Shield className="h-4 w-4 text-blue-500" />
                <span>Change Role</span>
              </button>

              {/* Reset Password */}
              <button
                onClick={() => handleAction(() => onResetPassword(user))}
                className="w-full px-4 py-2 text-left text-sm hover:bg-muted/50 transition-colors flex items-center gap-3"
              >
                <Key className="h-4 w-4 text-orange-500" />
                <span>Reset Password</span>
              </button>

              {/* Divider */}
              <div className="my-2 border-t border-border" />

              {/* Delete User */}
              <button
                onClick={() => handleAction(() => onDeleteUser(user))}
                className="w-full px-4 py-2 text-left text-sm hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors flex items-center gap-3 text-red-600 dark:text-red-400"
              >
                <Trash2 className="h-4 w-4" />
                <span>Delete User</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default UserActionsDropdown
