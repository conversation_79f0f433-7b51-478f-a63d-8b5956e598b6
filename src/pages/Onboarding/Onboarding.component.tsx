import React from 'react'
import { useTheme } from '../../services/theme/ThemeProvider'
import { OnboardingComponentProps, DifficultyOption, TopicOption } from './types'

/**
 * Onboarding Component - Pure UI component for user onboarding
 */
const OnboardingComponent: React.FC<OnboardingComponentProps> = ({
  formData,
  errors,
  isLoading,
  themes,
  themesLoading,
  onInputChange,
  onSubmit,
  onClearError,
}) => {
  const { theme } = useTheme()

  const difficultyOptions: DifficultyOption[] = [
    { value: 1, label: 'Easy', description: 'Perfect for beginners' },
    { value: 2, label: 'Medium', description: 'Some experience needed' },
    { value: 3, label: 'Hard', description: 'For advanced learners' },
  ]

  const handleThemeToggle = (themeId: string) => {
    const currentThemes = formData.preferred_topics
    const newThemes = currentThemes.includes(themeId)
      ? currentThemes.filter(t => t !== themeId)
      : [...currentThemes, themeId]
    onInputChange('preferred_topics', newThemes)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-2xl">
        <div className="bg-white dark:bg-gray-800 shadow-xl rounded-2xl px-8 py-10 border border-gray-200 dark:border-gray-700">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Welcome! Let's personalize your learning
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Tell us a bit about yourself to get started
            </p>
          </div>

          {/* General Error Display */}
          {errors.general && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center justify-between">
                <p className="text-sm text-red-600 dark:text-red-400">
                  {errors.general}
                </p>
                <button
                  onClick={onClearError}
                  className="text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          <form onSubmit={onSubmit} className="space-y-8">
            {/* Age Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                How old are you?
              </label>
              <input
                type="number"
                min="3"
                max="18"
                value={formData.age}
                onChange={(e) => onInputChange('age', parseInt(e.target.value) || 0)}
                disabled={isLoading}
                className={`
                  w-full px-4 py-3 border rounded-lg text-gray-900 dark:text-white bg-white dark:bg-gray-700
                  placeholder-gray-500 dark:placeholder-gray-400
                  focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent
                  disabled:opacity-50 disabled:cursor-not-allowed
                  transition-colors duration-200
                  ${errors.age 
                    ? 'border-red-500 focus:ring-red-500' 
                    : 'border-gray-300 dark:border-gray-600'
                  }
                `}
                placeholder="Enter your age"
              />
              {errors.age && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {errors.age}
                </p>
              )}
            </div>

            {/* Difficulty Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                What difficulty level would you like?
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {difficultyOptions.map((option) => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => onInputChange('difficulty_level', option.value)}
                    disabled={isLoading}
                    className={`
                      p-4 border rounded-lg text-left transition-all duration-200
                      disabled:opacity-50 disabled:cursor-not-allowed
                      ${formData.difficulty_level === option.value
                        ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300'
                        : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-700'
                      }
                    `}
                  >
                    <div className="font-medium text-gray-900 dark:text-white">
                      {option.label}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {option.description}
                    </div>
                  </button>
                ))}
              </div>
              {errors.difficulty_level && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {errors.difficulty_level}
                </p>
              )}
            </div>

            {/* Preferred Themes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                What themes interest you? (Optional)
              </label>

              {themesLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                  <span className="ml-2 text-gray-600 dark:text-gray-400">Loading themes...</span>
                </div>
              ) : themes.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  No themes available
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
                  {themes.map((theme) => (
                    <button
                      key={theme.id}
                      type="button"
                      onClick={() => handleThemeToggle(theme.id)}
                      disabled={isLoading}
                      className={`
                        p-3 border rounded-lg text-center transition-all duration-200
                        disabled:opacity-50 disabled:cursor-not-allowed
                        ${formData.preferred_topics.includes(theme.id)
                          ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300'
                          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-700'
                        }
                      `}
                      style={{
                        backgroundColor: formData.preferred_topics.includes(theme.id)
                          ? undefined
                          : theme.background_color + '20', // Add transparency
                        borderColor: formData.preferred_topics.includes(theme.id)
                          ? undefined
                          : theme.background_color
                      }}
                    >
                      <div className="text-2xl mb-1">{theme.icon}</div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {theme.name_en || theme.name}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {theme.category}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className={`
                w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-lg
                text-sm font-medium text-white bg-purple-600 hover:bg-purple-700
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500
                disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-purple-600
                transition-colors duration-200
                ${isLoading ? 'cursor-wait' : ''}
              `}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Setting up your profile...
                </>
              ) : (
                'Complete Setup'
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}

export default OnboardingComponent
