import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronRight, User, Target, Heart } from 'lucide-react'
import { OnboardingComponentProps, DifficultyOption } from './types'

/**
 * Multi-Step Onboarding Component - Beautiful step-by-step user onboarding
 */
const MultiStepOnboardingComponent: React.FC<OnboardingComponentProps> = ({
  currentStep,
  totalSteps,
  formData,
  errors,
  isLoading,
  themes,
  themesLoading,
  onInputChange,
  onNextStep,
  onPrevStep,
  onSubmit,
  onClearError,
}) => {
  const difficultyOptions: DifficultyOption[] = [
    { value: 1, label: 'Easy', description: 'Perfect for beginners' },
    { value: 2, label: 'Medium', description: 'Some experience needed' },
    { value: 3, label: 'Hard', description: 'For advanced learners' },
  ]

  const handleThemeToggle = (themeId: string) => {
    const currentThemes = formData.preferred_topics
    const newThemes = currentThemes.includes(themeId)
      ? currentThemes.filter(t => t !== themeId)
      : [...currentThemes, themeId]
    onInputChange('preferred_topics', newThemes)
  }

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1: return User
      case 2: return Target
      case 3: return Heart
      default: return User
    }
  }

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return "What's your age?"
      case 2: return "Choose your level"
      case 3: return "Pick your interests"
      default: return "Welcome!"
    }
  }

  const getStepDescription = () => {
    switch (currentStep) {
      case 1: return "Help us create age-appropriate content for you"
      case 2: return "We'll adjust the difficulty to match your skills"
      case 3: return "Select themes that spark your curiosity"
      default: return ""
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 1: return formData.age >= 3 && formData.age <= 18
      case 2: return formData.difficulty_level >= 1 && formData.difficulty_level <= 3
      case 3: return true // Optional step
      default: return false
    }
  }

  const progressPercentage = (currentStep / totalSteps) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-slate-800 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">

        {/* Step Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            {Array.from({ length: totalSteps }, (_, index) => {
              const stepNumber = index + 1
              const isCompleted = stepNumber < currentStep
              const isCurrent = stepNumber === currentStep
              const isUpcoming = stepNumber > currentStep

              return (
                <React.Fragment key={stepNumber}>
                  <motion.div
                    className="relative"
                    initial={false}
                    animate={{
                      scale: isCurrent ? 1.1 : 1,
                    }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  >
                    <motion.div
                      className={`
                        w-12 h-12 rounded-full flex items-center justify-center relative z-10
                        transition-all duration-300 ease-in-out
                        ${isCompleted
                          ? 'bg-green-500 text-white shadow-lg shadow-green-500/30'
                          : isCurrent
                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/30'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500'
                        }
                      `}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {isCompleted ? (
                        <motion.svg
                          className="w-6 h-6"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.2 }}
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </motion.svg>
                      ) : (
                        <motion.div
                          key={stepNumber}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          {React.createElement(getStepIcon(stepNumber), { className: "w-6 h-6" })}
                        </motion.div>
                      )}
                    </motion.div>

                    {/* Pulse animation for current step */}
                    {isCurrent && (
                      <motion.div
                        className="absolute inset-0 rounded-full bg-blue-600 opacity-20"
                        animate={{
                          scale: [1, 1.5, 1],
                          opacity: [0.2, 0, 0.2],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                    )}
                  </motion.div>

                  {/* Connector Line */}
                  {stepNumber < totalSteps && (
                    <motion.div
                      className={`
                        h-1 w-16 rounded-full transition-all duration-500 ease-in-out
                        ${stepNumber < currentStep ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'}
                      `}
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: stepNumber < currentStep ? 1 : 0.3 }}
                      transition={{ duration: 0.5, delay: stepNumber < currentStep ? 0.3 : 0 }}
                    />
                  )}
                </React.Fragment>
              )
            })}
          </div>
        </div>

        {/* Main Card */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >

          {/* Header */}
          <div className="text-center px-8 pt-8 pb-6">
            <motion.div
              className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg"
              whileHover={{ scale: 1.05, rotate: 5 }}
              transition={{ duration: 0.3 }}
            >
              <span className="text-2xl font-bold text-white">🎯</span>
            </motion.div>

            <motion.h1
              key={currentStep}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
              className="text-2xl font-bold text-gray-900 dark:text-white mb-2"
            >
              {getStepTitle()}
            </motion.h1>

            <motion.p
              key={`desc-${currentStep}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.1 }}
              className="text-gray-600 dark:text-gray-400 text-sm"
            >
              {getStepDescription()}
            </motion.p>
          </div>

          {/* Content */}
          <div className="px-10 pb-10">
            <form onSubmit={onSubmit}>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  {/* Step 1: Age */}
                  {currentStep === 1 && (
                    <div className="space-y-6">
                      <div className="relative z-50">
                        <select
                          value={formData.age || ""}
                          onChange={(e) => onInputChange('age', parseInt(e.target.value))}
                          className={`
                            w-full px-6 py-6 border-2 rounded-2xl text-center text-2xl font-bold
                            focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500
                            transition-all duration-200 appearance-none cursor-pointer
                            bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                            shadow-lg hover:shadow-xl relative z-50
                            ${errors.age
                              ? 'border-red-300 ring-red-500/20'
                              : 'border-gray-200 dark:border-gray-600 hover:border-blue-300'
                            }
                          `}
                        >
                          <option value="" disabled>Select your age</option>
                          {Array.from({ length: 96 }, (_, i) => i + 3).map(age => (
                            <option key={age} value={age} className="text-gray-900 dark:text-white bg-white dark:bg-gray-800">
                              {age}
                            </option>
                          ))}
                        </select>

                        {/* Custom dropdown arrow */}
                        <div className="absolute inset-y-0 right-4 flex items-center pointer-events-none z-10">
                          <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>

                        <div className="mt-4 text-center">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            Choose your age to get personalized content
                          </span>
                        </div>
                      </div>
                      {errors.age && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="text-sm text-red-600 dark:text-red-400 text-center"
                        >
                          {errors.age}
                        </motion.p>
                      )}
                    </div>
                  )}

                {/* Step 2: Difficulty */}
                {currentStep === 2 && (
                  <div className="space-y-6">
                    <div className="space-y-4">
                      {difficultyOptions.map((option) => (
                        <motion.label
                          key={option.value}
                          className={`
                            flex items-center p-6 border-2 rounded-2xl cursor-pointer transition-all duration-200
                            shadow-lg hover:shadow-xl
                            ${formData.difficulty_level === option.value
                              ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20'
                              : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 bg-white dark:bg-gray-800'
                            }
                          `}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="radio"
                            name="difficulty"
                            value={option.value}
                            checked={formData.difficulty_level === option.value}
                            onChange={(e) => onInputChange('difficulty_level', parseInt(e.target.value))}
                            className="sr-only"
                          />
                          <div className={`
                            w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center
                            ${formData.difficulty_level === option.value
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-gray-300 dark:border-gray-600'
                            }
                          `}>
                            {formData.difficulty_level === option.value && (
                              <div className="w-3 h-3 rounded-full bg-white" />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-lg text-gray-900 dark:text-white">
                              {option.label}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              {option.description}
                            </div>
                          </div>
                        </motion.label>
                      ))}
                    </div>
                    {errors.difficulty_level && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-sm text-red-600 dark:text-red-400 text-center"
                      >
                        {errors.difficulty_level}
                      </motion.p>
                    )}
                  </div>
                )}

                {/* Step 3: Themes */}
                {currentStep === 3 && (
                  <div className="space-y-6">
                    {themesLoading ? (
                      <div className="flex items-center justify-center py-12">
                        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"></div>
                        <span className="ml-3 text-gray-600 dark:text-gray-400 text-lg">Loading themes...</span>
                      </div>
                    ) : themes.length === 0 ? (
                      <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                        No themes available
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-4 max-h-80 overflow-y-auto pr-2">
                        {themes.map((theme) => (
                          <motion.button
                            key={theme.id}
                            type="button"
                            onClick={() => handleThemeToggle(theme.id)}
                            disabled={isLoading}
                            className={`
                              p-5 border-2 rounded-2xl text-center transition-all duration-200
                              disabled:opacity-50 disabled:cursor-not-allowed
                              shadow-lg hover:shadow-xl
                              ${formData.preferred_topics.includes(theme.id)
                                ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 text-blue-700 dark:text-blue-300'
                                : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 bg-white dark:bg-gray-800'
                              }
                            `}
                            style={{
                              backgroundColor: formData.preferred_topics.includes(theme.id)
                                ? undefined
                                : theme.background_color + '15',
                              borderColor: formData.preferred_topics.includes(theme.id)
                                ? undefined
                                : theme.background_color + '80'
                            }}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <div className="text-3xl mb-2">{theme.icon}</div>
                            <div className="text-sm font-semibold text-gray-900 dark:text-white">
                              {theme.name_en || theme.name}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                              {theme.category}
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    )}

                    {errors.preferred_topics && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-sm text-red-600 dark:text-red-400 text-center"
                      >
                        {errors.preferred_topics}
                      </motion.p>
                    )}

                    <div className="text-center">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Select at least one theme that interests you
                      </p>
                    </div>
                  </div>
                )}
                </motion.div>
              </AnimatePresence>

              {/* Navigation */}
              <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-100 dark:border-gray-700">
                <button
                  type="button"
                  onClick={onPrevStep}
                  disabled={currentStep === 1 || isLoading}
                  className={`
                    px-6 py-3 rounded-xl font-medium transition-all duration-200
                    ${currentStep === 1 || isLoading
                      ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }
                  `}
                >
                  Back
                </button>

              {currentStep < totalSteps ? (
                  <motion.button
                    type="button"
                    onClick={onNextStep}
                    disabled={!canProceed() || isLoading}
                    className={`
                      inline-flex items-center px-8 py-3 rounded-xl font-semibold transition-all duration-200
                      ${canProceed() && !isLoading
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                      }
                    `}
                    whileHover={canProceed() && !isLoading ? { scale: 1.05 } : {}}
                    whileTap={canProceed() && !isLoading ? { scale: 0.95 } : {}}
                  >
                    Continue
                    <ChevronRight className="w-5 h-5 ml-2" />
                  </motion.button>
                ) : (
                  <motion.button
                    type="submit"
                    disabled={isLoading}
                    className={`
                      inline-flex items-center px-8 py-3 rounded-xl font-semibold transition-all duration-200
                      ${!isLoading
                        ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                      }
                    `}
                    whileHover={!isLoading ? { scale: 1.05 } : {}}
                    whileTap={!isLoading ? { scale: 0.95 } : {}}
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Setting up...
                      </>
                    ) : (
                      <>
                        Complete Setup
                        <ChevronRight className="w-5 h-5 ml-2" />
                      </>
                    )}
                  </motion.button>
                )}
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default MultiStepOnboardingComponent
