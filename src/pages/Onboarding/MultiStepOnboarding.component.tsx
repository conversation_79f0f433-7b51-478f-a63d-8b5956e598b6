import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight, Check } from 'lucide-react'
import { OnboardingComponentProps, DifficultyOption } from './types'

/**
 * Multi-Step Onboarding Component - Step-by-step user onboarding
 */
const MultiStepOnboardingComponent: React.FC<OnboardingComponentProps> = ({
  currentStep,
  totalSteps,
  formData,
  errors,
  isLoading,
  themes,
  themesLoading,
  onInputChange,
  onNextStep,
  onPrevStep,
  onSubmit,
  onClearError,
}) => {
  const difficultyOptions: DifficultyOption[] = [
    { value: 1, label: 'Easy', description: 'Perfect for beginners' },
    { value: 2, label: 'Medium', description: 'Some experience needed' },
    { value: 3, label: 'Hard', description: 'For advanced learners' },
  ]

  const handleThemeToggle = (themeId: string) => {
    const currentThemes = formData.preferred_topics
    const newThemes = currentThemes.includes(themeId)
      ? currentThemes.filter(t => t !== themeId)
      : [...currentThemes, themeId]
    onInputChange('preferred_topics', newThemes)
  }

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return "Let's start with your age"
      case 2: return "Choose your difficulty level"
      case 3: return "Pick your favorite themes"
      default: return "Welcome!"
    }
  }

  const getStepDescription = () => {
    switch (currentStep) {
      case 1: return "This helps us create age-appropriate content for you"
      case 2: return "We'll adjust the complexity of questions based on your choice"
      case 3: return "Select themes that interest you (optional)"
      default: return ""
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 1: return formData.age >= 3 && formData.age <= 18
      case 2: return formData.difficulty_level >= 1 && formData.difficulty_level <= 3
      case 3: return true // Optional step
      default: return false
    }
  }

  const progressPercentage = (currentStep / totalSteps) * 100

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-2xl">
        <div className="bg-white dark:bg-gray-800 shadow-xl rounded-2xl px-8 py-10 border border-gray-200 dark:border-gray-700">
          
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Step {currentStep} of {totalSteps}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {Math.round(progressPercentage)}% Complete
              </span>
            </div>
            
            {/* Progress Steps */}
            <div className="flex items-center space-x-4 mb-4">
              {Array.from({ length: totalSteps }, (_, index) => {
                const stepNumber = index + 1
                const isCompleted = stepNumber < currentStep
                const isCurrent = stepNumber === currentStep
                
                return (
                  <div key={stepNumber} className="flex items-center">
                    <div
                      className={`
                        w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200
                        ${isCompleted 
                          ? 'bg-green-500 text-white' 
                          : isCurrent 
                            ? 'bg-purple-600 text-white' 
                            : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
                        }
                      `}
                    >
                      {isCompleted ? <Check className="w-4 h-4" /> : stepNumber}
                    </div>
                    {stepNumber < totalSteps && (
                      <div
                        className={`
                          w-12 h-1 mx-2 transition-all duration-200
                          ${stepNumber < currentStep ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-600'}
                        `}
                      />
                    )}
                  </div>
                )
              })}
            </div>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progressPercentage}%` }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              />
            </div>
          </div>

          {/* Header */}
          <div className="text-center mb-8">
            <motion.h1
              key={currentStep}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-3xl font-bold text-gray-900 dark:text-white mb-2"
            >
              {getStepTitle()}
            </motion.h1>
            <motion.p
              key={`desc-${currentStep}`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-gray-600 dark:text-gray-400"
            >
              {getStepDescription()}
            </motion.p>
          </div>

          {/* Error Display */}
          {errors.general && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
            >
              <p className="text-sm text-red-700 dark:text-red-400">{errors.general}</p>
              <button
                onClick={onClearError}
                className="mt-2 text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
              >
                Dismiss
              </button>
            </motion.div>
          )}

          {/* Step Content */}
          <form onSubmit={onSubmit} className="space-y-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {/* Step 1: Age */}
                {currentStep === 1 && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                        How old are you?
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          min="3"
                          max="18"
                          value={formData.age}
                          onChange={(e) => onInputChange('age', parseInt(e.target.value) || 3)}
                          className={`
                            w-full px-4 py-3 border rounded-lg text-center text-2xl font-bold
                            focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent
                            transition-colors duration-200
                            ${errors.age 
                              ? 'border-red-500 bg-red-50 dark:bg-red-900/20' 
                              : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                            }
                          `}
                          placeholder="Enter your age"
                        />
                        <div className="mt-2 text-center">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            Age range: 3-18 years
                          </span>
                        </div>
                      </div>
                      {errors.age && (
                        <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.age}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Step 2: Difficulty */}
                {currentStep === 2 && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                        What difficulty level would you like?
                      </label>
                      <div className="space-y-3">
                        {difficultyOptions.map((option) => (
                          <label
                            key={option.value}
                            className={`
                              flex items-center p-4 border rounded-lg cursor-pointer transition-all duration-200
                              ${formData.difficulty_level === option.value
                                ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-700'
                              }
                            `}
                          >
                            <input
                              type="radio"
                              name="difficulty"
                              value={option.value}
                              checked={formData.difficulty_level === option.value}
                              onChange={(e) => onInputChange('difficulty_level', parseInt(e.target.value))}
                              className="sr-only"
                            />
                            <div className={`
                              w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center
                              ${formData.difficulty_level === option.value
                                ? 'border-purple-500 bg-purple-500'
                                : 'border-gray-300 dark:border-gray-600'
                              }
                            `}>
                              {formData.difficulty_level === option.value && (
                                <div className="w-2 h-2 rounded-full bg-white" />
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-gray-900 dark:text-white">
                                {option.label}
                              </div>
                              <div className="text-sm text-gray-600 dark:text-gray-400">
                                {option.description}
                              </div>
                            </div>
                          </label>
                        ))}
                      </div>
                      {errors.difficulty_level && (
                        <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.difficulty_level}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Step 3: Themes */}
                {currentStep === 3 && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                        What themes interest you? (Optional)
                      </label>
                      
                      {themesLoading ? (
                        <div className="flex items-center justify-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                          <span className="ml-2 text-gray-600 dark:text-gray-400">Loading themes...</span>
                        </div>
                      ) : themes.length === 0 ? (
                        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                          No themes available
                        </div>
                      ) : (
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
                          {themes.map((theme) => (
                            <button
                              key={theme.id}
                              type="button"
                              onClick={() => handleThemeToggle(theme.id)}
                              disabled={isLoading}
                              className={`
                                p-3 border rounded-lg text-center transition-all duration-200
                                disabled:opacity-50 disabled:cursor-not-allowed
                                ${formData.preferred_topics.includes(theme.id)
                                  ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300'
                                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-700'
                                }
                              `}
                              style={{
                                backgroundColor: formData.preferred_topics.includes(theme.id) 
                                  ? undefined 
                                  : theme.background_color + '20',
                                borderColor: formData.preferred_topics.includes(theme.id) 
                                  ? undefined 
                                  : theme.background_color
                              }}
                            >
                              <div className="text-2xl mb-1">{theme.icon}</div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {theme.name_en || theme.name}
                              </div>
                              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                {theme.category}
                              </div>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </motion.div>
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between pt-6">
              <button
                type="button"
                onClick={onPrevStep}
                disabled={currentStep === 1 || isLoading}
                className={`
                  inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium
                  transition-colors duration-200
                  ${currentStep === 1 || isLoading
                    ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                  }
                `}
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </button>

              {currentStep < totalSteps ? (
                <button
                  type="button"
                  onClick={onNextStep}
                  disabled={!canProceed() || isLoading}
                  className={`
                    inline-flex items-center px-6 py-2 rounded-lg text-sm font-medium
                    transition-colors duration-200
                    ${canProceed() && !isLoading
                      ? 'bg-purple-600 hover:bg-purple-700 text-white'
                      : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                    }
                  `}
                >
                  Next
                  <ChevronRight className="w-4 h-4 ml-1" />
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`
                    inline-flex items-center px-6 py-2 rounded-lg text-sm font-medium
                    transition-colors duration-200
                    ${!isLoading
                      ? 'bg-green-600 hover:bg-green-700 text-white'
                      : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                    }
                  `}
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Completing...
                    </>
                  ) : (
                    <>
                      <Check className="w-4 h-4 mr-1" />
                      Complete Setup
                    </>
                  )}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default MultiStepOnboardingComponent
