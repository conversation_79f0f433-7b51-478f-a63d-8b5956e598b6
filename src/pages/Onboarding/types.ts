export interface OnboardingFormData {
  age: number
  difficulty_level: number
  preferred_topics: string[] // Theme IDs
}

export interface OnboardingStepData {
  step1?: { age: number }
  step2?: { difficulty_level: number }
  step3?: { preferred_topics: string[] }
}

export interface OnboardingStatus {
  onboarding_completed: boolean
  preferred_topics: string[]
  age?: number
  difficulty_level?: number
}

export interface OnboardingFormErrors {
  age?: string
  difficulty_level?: string
  preferred_topics?: string
  general?: string
}

export interface OnboardingContainerProps {
  // Props passed from route/parent
}

export interface OnboardingComponentProps {
  currentStep: number
  totalSteps: number
  formData: OnboardingFormData
  errors: OnboardingFormErrors
  isLoading: boolean
  themes: ThemeOption[]
  themesLoading: boolean
  onInputChange: (field: keyof OnboardingFormData, value: any) => void
  onNextStep: () => void
  onPrevStep: () => void
  onSubmit: (e: React.FormEvent) => void
  onClearError: () => void
}

export interface DifficultyOption {
  value: number
  label: string
  description: string
}

export interface TopicOption {
  value: string
  label: string
  icon?: string
}

export interface ThemeOption {
  id: string
  name: string
  name_en: string
  description: string
  description_en: string
  category: string
  icon: string
  background_color: string
  font_color: string
}

export interface CharacterOption {
  id: string
  name: string
  gender: 'male' | 'female'
  emoji: string
  description: string
  live2dModel?: string
  color: string
}

export interface GenderOption {
  value: 'male' | 'female'
  label: string
  icon: string
  description: string
}

export interface CharacterOption {
  id: string
  name: string
  type: string
  gender: 'male' | 'female'
  preview: string
  description: string
  live2dModel?: string
}
