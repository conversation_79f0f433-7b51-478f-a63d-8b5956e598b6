import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../../store/hooks'
import { submitOnboardingData, getOnboardingStatus, clearError } from '../../store/slices/authSlice'
import MultiStepOnboardingComponent from './MultiStepOnboarding.component'
import { OnboardingFormData, OnboardingFormErrors, ThemeOption } from './types'
import { OnboardingData } from '../../types/auth'
import { CuratedService } from '../../services/curatedService'

/**
 * Multi-Step Onboarding Container - Handles step-by-step onboarding logic
 */
const MultiStepOnboardingContainer: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const { user, isLoading, error } = useAppSelector((state) => state.auth)

  // Step management
  const [currentStep, setCurrentStep] = useState(1)
  const totalSteps = 3

  // Form data
  const [formData, setFormData] = useState<OnboardingFormData>({
    age: 8,
    difficulty_level: 1,
    preferred_topics: []
  })

  // Form errors
  const [formErrors, setFormErrors] = useState<OnboardingFormErrors>({})

  // Themes state
  const [themes, setThemes] = useState<ThemeOption[]>([])
  const [themesLoading, setThemesLoading] = useState(true)

  // Check onboarding status and redirect if needed
  useEffect(() => {
    if (!user) {
      navigate('/login')
      return
    }

    // Fetch current onboarding status
    const checkOnboardingStatus = async () => {
      try {
        const result = await dispatch(getOnboardingStatus()).unwrap()

        if (result.onboarding_completed) {
          navigate('/dashboard')
          return
        }

        // If partial data exists, determine which step to start from
        if (result.age) {
          setFormData(prev => ({ ...prev, age: result.age }))
          if (result.difficulty_level) {
            setFormData(prev => ({ ...prev, difficulty_level: result.difficulty_level }))
            setCurrentStep(3) // Go to themes step
          } else {
            setCurrentStep(2) // Go to difficulty step
          }
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error)
      }
    }

    checkOnboardingStatus()
  }, [user, navigate, dispatch])

  // Fetch themes on component mount
  useEffect(() => {
    const fetchThemes = async () => {
      try {
        setThemesLoading(true)
        
        const response = await CuratedService.getThemes({
          limit: 100,
          is_active: true
        })
        
        const themeOptions: ThemeOption[] = response.data.map(theme => ({
          id: theme._id || theme.id || '',
          name: theme.name,
          name_en: theme.name_en,
          description: theme.description,
          description_en: theme.description_en,
          category: theme.category,
          icon: theme.icon,
          background_color: theme.background_color,
          font_color: theme.font_color
        }))
        
        setThemes(themeOptions)
      } catch (error) {
        console.error('Error fetching themes:', error)
      } finally {
        setThemesLoading(false)
      }
    }

    fetchThemes()
  }, [])

  // Handle input changes
  const handleInputChange = (field: keyof OnboardingFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear field-specific error
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Validate current step
  const validateStep = (step: number): boolean => {
    const errors: OnboardingFormErrors = {}

    switch (step) {
      case 1:
        if (!formData.age || formData.age < 3) {
          errors.age = 'Please select your age'
        }
        break
      case 2:
        if (!formData.difficulty_level || formData.difficulty_level < 1 || formData.difficulty_level > 3) {
          errors.difficulty_level = 'Please select a difficulty level'
        }
        break
      case 3:
        if (!formData.preferred_topics || formData.preferred_topics.length === 0) {
          errors.preferred_topics = 'Please select at least one theme that interests you'
        }
        break
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle next step
  const handleNextStep = async () => {
    if (!validateStep(currentStep)) {
      return
    }

    // Submit current step data to backend
    try {
      let stepData: OnboardingData = {}

      switch (currentStep) {
        case 1:
          // Only send age for step 1
          stepData = {
            age: formData.age
          }
          break
        case 2:
          // Send age and difficulty for step 2
          stepData = {
            age: formData.age,
            difficulty_level: formData.difficulty_level
          }
          break
      }

      // Submit partial data (don't include preferred_topics until final step)
      await dispatch(submitOnboardingData(stepData)).unwrap()

      // Move to next step
      setCurrentStep(prev => Math.min(prev + 1, totalSteps))
    } catch (error: any) {
      setFormErrors({ general: error.message || 'Failed to save progress' })
    }
  }

  // Handle previous step
  const handlePrevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  // Handle final submission (step 3 - themes)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateStep(currentStep)) {
      return
    }

    try {
      // Only on final step (3), send complete data including themes
      const finalData: OnboardingData = {
        age: formData.age,
        difficulty_level: formData.difficulty_level,
        preferred_topics: formData.preferred_topics // Only include themes on final submission
      }

      await dispatch(submitOnboardingData(finalData)).unwrap()
      navigate('/dashboard')
    } catch (error: any) {
      setFormErrors({ general: error.message || 'Failed to complete onboarding' })
    }
  }

  // Clear errors
  const handleClearError = () => {
    dispatch(clearError())
    setFormErrors({})
  }

  // Don't render if user is not authenticated
  if (!user) {
    return null
  }

  return (
    <MultiStepOnboardingComponent
      currentStep={currentStep}
      totalSteps={totalSteps}
      formData={formData}
      errors={formErrors}
      isLoading={isLoading}
      themes={themes}
      themesLoading={themesLoading}
      onInputChange={handleInputChange}
      onNextStep={handleNextStep}
      onPrevStep={handlePrevStep}
      onSubmit={handleSubmit}
      onClearError={handleClearError}
    />
  )
}

export default MultiStepOnboardingContainer
