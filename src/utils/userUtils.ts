import { PreferredTopic } from '../types/auth'

/**
 * Utility functions for handling user data with backward compatibility
 */

/**
 * Normalize preferred topics to handle both string[] and PreferredTopic[] formats
 * @param topics - Can be string[] or PreferredTopic[]
 * @returns Normalized array of topic objects with display information
 */
export const normalizePreferredTopics = (
  topics: string[] | PreferredTopic[] | undefined
): Array<{ name: string; name_en?: string; id?: string; displayName: string }> => {
  if (!topics || !Array.isArray(topics)) {
    return []
  }

  return topics.map((topic, index) => {
    if (typeof topic === 'string') {
      // Backward compatibility: string format
      return {
        name: topic,
        displayName: topic,
        id: `legacy-${index}`
      }
    } else {
      // New format: object with name, name_en, id
      return {
        name: topic.name,
        name_en: topic.name_en,
        id: topic.id,
        displayName: topic.name_en ? `${topic.name} (${topic.name_en})` : topic.name
      }
    }
  })
}

/**
 * Get display name for a single topic
 * @param topic - Can be string or PreferredTopic
 * @returns Display name for the topic
 */
export const getTopicDisplayName = (topic: string | PreferredTopic): string => {
  if (typeof topic === 'string') {
    return topic
  }
  return topic.name_en ? `${topic.name} (${topic.name_en})` : topic.name
}

/**
 * Check if preferred topics are in the new object format
 * @param topics - Topics array to check
 * @returns true if topics are in object format, false if string format
 */
export const isNewTopicFormat = (topics: string[] | PreferredTopic[] | undefined): topics is PreferredTopic[] => {
  if (!topics || !Array.isArray(topics) || topics.length === 0) {
    return false
  }
  return typeof topics[0] === 'object' && 'name' in topics[0]
}

/**
 * Convert topics to the format expected by the API
 * @param topics - Topics in either format
 * @returns Topics in the format expected by the API (usually string[] for backward compatibility)
 */
export const topicsToApiFormat = (topics: string[] | PreferredTopic[] | undefined): string[] => {
  if (!topics || !Array.isArray(topics)) {
    return []
  }

  return topics.map(topic => {
    if (typeof topic === 'string') {
      return topic
    }
    return topic.id || topic.name // Prefer ID, fallback to name
  })
}
