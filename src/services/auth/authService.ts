import httpBase, { RequestCallbacks } from '../http/httpBase'
import {
  LoginCredentials,
  SignupCredentials,
  GoogleAuthCredentials,
  OnboardingData,
  LoginResponse,
  SignupResponse,
  OnboardingResponse,
  User
} from '../../types/auth'

class AuthService {
  // Get client ID from environment
  private getClientId(): string {
    return import.meta.env.VITE_DEFAULT_TENANT_SLUG || 'test'
  }

  // Login user
  async login(
    credentials: LoginCredentials,
    callbacks?: RequestCallbacks<LoginResponse>
  ): Promise<LoginResponse> {
    // Create form data as required by the API
    const formData = new URLSearchParams()
    formData.append('grant_type', 'password')
    formData.append('username', credentials.email)
    formData.append('password', credentials.password)
    formData.append('scope', '')
    formData.append('client_id', credentials.client_id || this.getClientId())

    const response = await httpBase.post<LoginResponse>(
      '/auth/login/editor',
      formData.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      },
      callbacks
    )

    // Set auth token in httpBase
    if (response.data.access_token) {
      httpBase.setAuthToken(response.data.access_token)
      localStorage.setItem('auth_token', response.data.access_token)
    }
    if (callbacks?.onSuccess) {
      callbacks.onSuccess(response.data)
    }
    // if 403 error, throw an error
    if (response.status === 403) {
      throw new Error('Invalid credentials or account not found')
    }

    return response.data
  }

  // Signup user
  async signup(
    credentials: SignupCredentials,
    callbacks?: RequestCallbacks<SignupResponse>
  ): Promise<SignupResponse> {
    // Create form data as required by the API
    const formData = new URLSearchParams()
    formData.append('username', credentials.username)
    formData.append('email', credentials.email)
    formData.append('password', credentials.password)
    formData.append('client_id', credentials.client_id || this.getClientId())

    if (credentials.full_name) {
      formData.append('full_name', credentials.full_name)
    }

    const response = await httpBase.post<SignupResponse>(
      '/auth/signup',
      formData.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      },
      callbacks
    )

    // Signup now returns the same format as login (auto-login)
    if (response.data.access_token) {
      httpBase.setAuthToken(response.data.access_token)
      localStorage.setItem('auth_token', response.data.access_token)
    }

    return response.data
  }

  // Google authentication
  async googleAuth(
    credentials: GoogleAuthCredentials,
    callbacks?: RequestCallbacks<LoginResponse>
  ): Promise<LoginResponse> {
    const response = await httpBase.post<LoginResponse>(
      '/auth/google-auth',
      {
        id_token: credentials.id_token,
        client_id: credentials.client_id || this.getClientId()
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      },
      callbacks
    )

    // Set auth token in httpBase
    if (response.data.access_token) {
      httpBase.setAuthToken(response.data.access_token)
      localStorage.setItem('auth_token', response.data.access_token)
    }

    return response.data
  }

  // Submit onboarding data
  async submitOnboarding(
    data: OnboardingData,
    callbacks?: RequestCallbacks<OnboardingResponse>
  ): Promise<OnboardingResponse> {
    const response = await httpBase.post<OnboardingResponse>(
      '/auth/onboarding',
      data,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      },
      callbacks
    )

    return response.data
  }

  // Get onboarding status
  async getOnboardingStatus(
    callbacks?: RequestCallbacks<{
      onboarding_completed: boolean
      preferred_topics: string[]
      age?: number
      difficulty_level?: number
    }>
  ): Promise<{
    onboarding_completed: boolean
    preferred_topics: string[]
    age?: number
    difficulty_level?: number
  }> {
    const response = await httpBase.get<{
      onboarding_completed: boolean
      preferred_topics: string[]
      age?: number
      difficulty_level?: number
    }>('/auth/onboarding', {}, callbacks)
    return response.data
  }

  // Logout user
  async logout(callbacks?: RequestCallbacks<void>): Promise<void> {
    try {
      await httpBase.post('/auth/logout', {}, {}, callbacks)
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed, continuing with local logout')
    } finally {
      httpBase.clearAuthToken()
      localStorage.removeItem('auth_token')
    }
  }

  // Get current user - now uses verify_token endpoint
  async getCurrentUser(callbacks?: RequestCallbacks<User>): Promise<User> {
    // verify_token returns user data directly with 200 if valid, 401 if invalid
    const response = await httpBase.get<User>('/auth/verify_token', {}, callbacks)

    return response.data
  }

  // Get user profile with detailed information
  async getUserProfile(callbacks?: RequestCallbacks<any>): Promise<any> {
    const response = await httpBase.get<any>('/auth/profile', {}, callbacks)
    return response.data
  }

  // Refresh token
  async refreshToken(callbacks?: RequestCallbacks<{ token: string }>): Promise<string> {
    const response = await httpBase.post<{ token: string }>('/auth/refresh', {}, {}, callbacks)
    
    if (response.data.token) {
      httpBase.setAuthToken(response.data.token)
    }
    
    return response.data.token
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token')
  }

  // Get stored token
  getStoredToken(): string | null {
    return localStorage.getItem('auth_token')
  }
}

export const authService = new AuthService()
export default authService
