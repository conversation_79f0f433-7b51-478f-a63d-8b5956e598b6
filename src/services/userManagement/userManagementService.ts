import httpBase, { RequestCallbacks } from '../http/httpBase'

// Types for User Management
export interface User {
  id: string
  username: string
  full_name: string
  email: string
  role: 'admin' | 'supervisor' | 'agent'
  created_at: string
  last_login: string | null
  onboarding_completed: boolean
}

export interface UsersListResponse {
  data: User[]
  meta: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

export interface InviteUserRequest {
  username: string
  role: 'admin' | 'supervisor' | 'agent'
}

export interface InviteUserResponse {
  registration_token: string
  success: boolean
  msg: string
}

export interface RegisterUserRequest {
  username: string
  role: string
  password: string
  token: string
}

export interface RegisterUserResponse {
  success: boolean
  msg: string
}

export interface ResetPasswordRequest {
  username: string
}

export interface ResetPasswordResponse {
  message: string
  new_password?: string
  username?: string
  reset_by?: string
  reset_at?: string
}

export interface ChangeRoleRequest {
  new_role: string
}

export interface ChangeRoleResponse {
  message: string
  user_id: string
  username: string
  old_role: string
  new_role: string
  changed_by: string
  changed_at: string
}

export interface ChangePasswordRequest {
  old_password: string
  new_password: string
}

export interface ChangePasswordResponse {
  message: string
  username?: string
  changed_at?: string
}

export interface DeleteUserResponse {
  message: string
  user_id: string
  username: string
  deleted_by: string
  deleted_at: string
}

export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  role?: string
}

/**
 * User Management Service
 * Handles all user-related API operations for admin dashboard
 */
class UserManagementService {
  /**
   * Get list of users with pagination and filtering
   */
  async getUsers(
    params: PaginationParams = {},
    callbacks?: RequestCallbacks<UsersListResponse>
  ): Promise<UsersListResponse> {
    const queryParams = new URLSearchParams()

    if (params.page) queryParams.append('page', params.page.toString())
    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.search) queryParams.append('search', params.search)
    if (params.role) queryParams.append('role', params.role)

    const url = `/auth/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

    const response = await httpBase.get<UsersListResponse>(url, {}, callbacks)
    return response.data
  }

  /**
   * Invite a new user
   */
  async inviteUser(
    data: InviteUserRequest,
    callbacks?: RequestCallbacks<InviteUserResponse>
  ): Promise<InviteUserResponse> {
    const response = await httpBase.post<InviteUserResponse>(
      '/auth/users/invite',
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      callbacks
    )
    return response.data
  }

  /**
   * Register a new user with invitation token
   */
  async registerUser(
    data: RegisterUserRequest,
    callbacks?: RequestCallbacks<RegisterUserResponse>
  ): Promise<RegisterUserResponse> {
    const response = await httpBase.post<RegisterUserResponse>(
      '/auth/users/register',
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      callbacks
    )
    return response.data
  }

  /**
   * Delete a user
   */
  async deleteUser(
    userId: string,
    callbacks?: RequestCallbacks<DeleteUserResponse>
  ): Promise<DeleteUserResponse> {
    const response = await httpBase.delete<DeleteUserResponse>(
      `/auth/users/${userId}`,
      {},
      callbacks
    )
    return response.data
  }

  /**
   * Reset user password (Admin only)
   */
  async resetPassword(
    data: ResetPasswordRequest,
    callbacks?: RequestCallbacks<ResetPasswordResponse>
  ): Promise<ResetPasswordResponse> {
    const response = await httpBase.post<ResetPasswordResponse>(
      '/auth/users/reset_password',
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      callbacks
    )
    return response.data
  }

  /**
   * Change user role (Admin only)
   */
  async changeUserRole(
    userId: string,
    data: ChangeRoleRequest,
    callbacks?: RequestCallbacks<ChangeRoleResponse>
  ): Promise<ChangeRoleResponse> {
    const response = await httpBase.put<ChangeRoleResponse>(
      `/auth/users/${userId}/role`,
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      callbacks
    )
    return response.data
  }

  /**
   * Change own password
   */
  async changePassword(
    data: ChangePasswordRequest,
    callbacks?: RequestCallbacks<ChangePasswordResponse>
  ): Promise<ChangePasswordResponse> {
    const response = await httpBase.post<ChangePasswordResponse>(
      '/auth/users/change_password',
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      callbacks
    )
    return response.data
  }
}

// Export singleton instance
const userManagementService = new UserManagementService()
export default userManagementService
